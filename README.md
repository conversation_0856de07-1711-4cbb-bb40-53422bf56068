# QFlowAgent

[![CI](https://github.com/langchain-ai/react-agent/actions/workflows/unit-tests.yml/badge.svg)](https://github.com/langchain-ai/react-agent/actions/workflows/unit-tests.yml)
[![Integration Tests](https://github.com/langchain-ai/react-agent/actions/workflows/integration-tests.yml/badge.svg)](https://github.com/langchain-ai/react-agent/actions/workflows/integration-tests.yml)

# act

```
act --env-file .env
```

# 子仓库

git submodule add https://github.com/x1xhlol/system-prompts-and-models-of-ai-tools.git 提示词收集/system-prompts-and-models-of-ai-tools
git submodule update --init --recursive

# install

```python
uv venv --python 3.12
source .venv/bin/activate
uv pip install -U langgraph duckdb langchain dotenv langchain-openai langchain_tavily "httpx[socks]" 
uv sync
uv pip install ".[test]"
uv pip install ".[dev]"

```

# 调试图

方法一： uv安装二进制

```python
uv pip install "langgraph-cli[inmem]" 
langgraph dev
```

方法二 ：或直接使用uvx

```python
uvx --refresh --from "langgraph-cli[inmem]" --python 3.13 langgraph dev
```

如果进程退出失败

```bash
lsof -ti:2024 | xargs kill -9
```

# cli测试图

```python
uv run cliclient.py
```

# 使用PG管理checkpoint和memory

```
uv add -U psycopg psycopg-pool langgraph langgraph-checkpoint-postgres
uv add -U "psycopg[binary,pool]"
```

# 创建 支持向量存储的 pgvector 容器

```bash
docker run --name qflow_agent_db -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres -e POSTGRES_DB=qflow -p 5432:5432 -d pgvector/pgvector:pg17
```

```python
DB_URI= "postgresql://postgres:postgres@localhost:5432/qflow?sslmode=disable"
```

> 如果不需要向量存储，则直接使用pg
>
> ```bash
> docker run --name qflow_agent_db -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres -e POSTGRES_DB=qflow -d -p 5432:5432 postgres
> ```



# 升级依赖


```bash
uv add langgraph --upgrade
```

# langgraph dev 调试
```bash
uv add -U "langgraph-cli[inmem]"
uv pip install -e .
uv run langgraph dev --allow-blocking
```


