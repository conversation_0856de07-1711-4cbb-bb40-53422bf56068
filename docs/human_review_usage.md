# 人工审核节点使用指南

## 概述

人工审核节点（`human_review_node`）是一个基于文件系统的简化审核系统，允许用户在创作流程中对AI生成的内容进行人工审核和修改。

## 功能特性

1. **基于文件**：直接从文件系统读取内容进行审核
2. **简化设计**：无需复杂的数据结构，通过状态字段传递参数
3. **交互式审核**：支持用户输入建议进行内容修改
4. **循环审核**：持续审核直到用户满意
5. **实时保存**：修改后的内容立即保存到文件

## 使用方式

### 1. 启用人工审核

在运行图时，在配置中设置 `enable_human_review` 为 `True`：

```python
config = {
    "configurable": {
        "enable_human_review": True,
        # 其他配置...
    }
}
```

### 2. 用户交互

当进入审核环节时，系统会显示：

```
================================================================================
🔍 【书籍设定步骤：世界观设定 审核】
================================================================================
这里显示需要审核的内容...
================================================================================

请审核以上内容：
- 输入 '/pass' 通过审核
- 输入修改建议，AI将根据建议重新生成
- 输入内容：
```

### 3. 审核操作

用户有两种选择：

#### 通过审核
输入 `/pass` 直接通过审核，继续后续流程。

#### 提供修改建议
输入具体的修改建议，例如：
```
请添加更多关于魔法体系的细节，包括魔法等级划分和学习方式
```

系统会根据建议重新生成内容，自动保存到文件，并再次展示供审核。

## 技术实现

### 核心组件

1. **HumanReviewParams**：审核参数数据类型，包含所有审核需要的信息
2. **human_review_node**：主要审核逻辑，基于文件系统，支持structured_request
3. **bs_step_condition_edge**：条件边，检查是否需要跳转到审核节点
4. **状态管理**：通过HumanReviewParams传递审核参数

### 集成步骤

对于其他创作节点，集成人工审核需要：

1. 导入必要模块：
```python
from src.nodes.common.book_types import HumanReviewParams
from langchain_core.load.dump import dumps
```

2. 在节点完成后检查是否需要审核：
```python
enable_human_review = config["configurable"].get("enable_human_review", False)
if enable_human_review:
    # 确定下一个节点
    next_node = "下一个应该跳转的节点"  # 根据业务逻辑确定
    
    # 创建审核参数
    human_review_params = HumanReviewParams(
        content_to_review=generated_content,
        llm_node_name=node_name,
        llm_input_messages=dumps(input_messages),
        file_name=file_name,
        output_type=None,  # 或具体的类型字符串
        next_node=next_node
    )
    
    # 保存文件
    FileSystemUtils.write_content_to_workspace(file_name, generated_content)
    
    state_update["human_review_params"] = human_review_params
    return Command(update=state_update)
```

3. 修改条件边函数，添加审核检查：
```python
def your_condition_edge(state: State, config: RunnableConfig):
    # 检查是否有人工审核参数
    human_review_params = state.get("human_review_params")
    if human_review_params:
        return "human_review"
    
    # 原有的条件逻辑
    # ...
```

4. 在图中添加审核节点和相应的边

## 状态管理

### 新增状态字段

- `human_review_params`：HumanReviewParams类型，包含所有审核参数

### HumanReviewParams 字段

- `content_to_review`：需要审核的内容
- `llm_node_name`：LLM节点名称
- `llm_input_messages`：序列化的输入消息
- `file_name`：文件名
- `output_type`：输出类型（None时使用astream，否则使用structured_request）
- `next_node`：审核通过后跳转的节点

### 数据流

1. 创作节点生成内容并保存到文件
2. 如果启用审核，创建HumanReviewParams并设置到state
3. 条件边检查到审核参数，跳转到 `human_review` 节点
4. 审核节点从文件读取内容展示给用户
5. 用户进行审核，可能多次修改（修改后立即保存到文件）
6. 审核通过后清理状态并跳转到指定的next_node

## 扩展性

这个架构设计支持：

- **多种输出类型**：支持普通文本和结构化输出（structured_request）
- **灵活的流程控制**：通过next_node字段控制审核后的跳转
- **基于文件的管理**：内容直接保存到文件系统
- **原始消息复用**：保留完整的输入上下文
- **条件化跳转**：通过条件边实现智能路由

### 高级特性

1. **结构化输出审核**：
   ```python
   # 在创建HumanReviewParams时设置output_type
   human_review_params = HumanReviewParams(
       # ... 其他参数
       output_type="src.nodes.common.book_types.WriterPlan"  # 类的完整路径
   )
   ```

2. **动态next_node**：
   ```python
   # 根据业务逻辑动态确定下一个节点
   next_node = "bs_refine" if need_refine else "bs_steps"
   ```

只需按照集成步骤，任何创作节点都可以轻松集成人工审核功能。新的架构更加灵活和强大。