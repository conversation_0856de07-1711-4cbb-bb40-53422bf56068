from typing import List

from langchain_core.messages import AIMessage, BaseMessage
from langchain_core.tools import InjectedToolCallId, tool

from langgraph.prebuilt import InjectedState, ToolNode
from typing_extensions import Annotated, TypedDict


class AgentState(dict):
    messages: List[BaseMessage]
    foo: str = ""
    xxxx: str = ""


@tool()
def state_tool(
    x: int,
    state: Annotated[dict, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId],
) -> str:
    """Do something with state."""
    if len(state["messages"]) > 2:
        return state["foo"] + str(x)
    else:
        return "not enough messages"


@tool
def foo_tool(x: int, foo: Annotated[str, InjectedState("foo")]) -> str:
    """Do something else with state."""
    return foo + str(x + 1)


node = ToolNode([state_tool, foo_tool])

tool_call1 = {"name": "state_tool", "args": {"x": 1}, "id": "1", "type": "tool_call"}
tool_call2 = {"name": "foo_tool", "args": {"x": 1}, "id": "2", "type": "tool_call"}
state = {
    "messages": [AIMessage("", tool_calls=[tool_call1, tool_call2])],
    "foo": "bar",
}
ret = node.invoke(state)
print(ret)
