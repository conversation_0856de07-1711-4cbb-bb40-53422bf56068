import asyncio
import logging
import os
from pathlib import Path

from dotenv import load_dotenv
from langchain_community.tools.file_management.read import ReadFileTool
from langchain_core.messages.utils import count_tokens_approximately, trim_messages
from langchain_core.vectorstores import InMemoryVectorStore
from langchain_postgres import PGVector
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.store.memory import InMemoryStore
from langgraph.store.postgres.aio import AsyncPostgresStore


if __name__ == "__main__":
    import sys

    sys.path.append(str(Path(__file__).parent.parent.parent))

from src.common.llms import get_default_embedding_model
from src.common.utils.path_utils import get_workspace_root
from src.init_log import init_log
from src.memory.store_utils import *

DB_URI = "postgresql://postgres:postgres@localhost:5432/qflow?sslmode=disable"

from langchain_community.embeddings import DashScopeEmbeddings
from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings

load_dotenv(dotenv_path="src/common/models/.env")
init_log()


def ali_dashscope_embedding_test():
    ali_embeddings = get_default_embedding_model()

    text = "This is a test document."

    query_result = ali_embeddings.embed_query(text)
    print("文本向量长度：", len(query_result), sep="")

    doc_results = ali_embeddings.embed_documents(
        ["Hi there!", "Oh, hello!", "What's your name?", "My friends call me World", "Hello World!"]
    )
    print("文本向量数量：", len(doc_results), "，文本向量长度：", len(doc_results[0]), sep="")


async def store_test():
    """新版本的向量存储测试"""
    try:
        # 检查环境变量
        if not os.getenv("ALI_API_KEY"):
            print("❌ 请设置 ALI_API_KEY 环境变量")
            return

        workspace_path = get_workspace_root()
        print(f"{workspace_path=}")

        file_name = "完整策划案.md"
        setting_file = workspace_path / file_name
        if not setting_file.exists():
            print(f"设定集文件不存在: {setting_file}")
            return

        print(f"找到设定集文件: {setting_file}")
        read_file_tool = ReadFileTool(root_dir=str(workspace_path))
        content = read_file_tool.invoke({"file_path": f"./{file_name}"})
        print(f"{len(content)=}")
        if not content:
            print(f"❌ 读取文件失败: {setting_file}")
            return

        async with (
            AsyncPostgresStore.from_conn_string(
                DB_URI,
                index={
                    "dims": 1024,
                    "embed": get_default_embedding_model(),
                },
            ) as store,
            AsyncPostgresSaver.from_conn_string(DB_URI) as checkpointer,
        ):
            await store.setup()
            await checkpointer.setup()
            docs = store_split_content_to_docs(content, {"source": "book_set"})
            print(f"{len(docs)=}")
            for i, doc in enumerate(docs):
                doc.id = f"book_set_{i}"
                print(f"chunk {i}, {docs[i]=}")
                print(f"chunk {i}, id={doc.id}")
                print(f"chunk {i}, 长度 = {len(doc.page_content)}")
                print(f"chunk {i}, metadata = {doc.metadata}")
                print("-" * 100)

            # docs总长度
            total_length = sum(len(doc.page_content) for doc in docs)
            print(f"total_length = {total_length}, origin_length = {len(content)}")
            # 设定集：("book_app", "book_id_test", "book_set")
            # 章节：("book_app", "book_id_test", "chapter")
            namespace = ("book_app", "book_id_test", "book_set")

            for doc in docs:
                # 保存到 AsyncPostgresStore，会自动建立向量索引
                await store_aput(
                    store=store,
                    namespace=namespace,
                    key=doc.id,
                    content=doc.page_content,
                    metadata=doc.metadata,
                    index=["content"],
                )
    except Exception as e:
        logging.error(f"Error in store_test: {e}", exc_info=True)
        raise


async def query_test():
    try:
        async with (
            AsyncPostgresStore.from_conn_string(
                DB_URI,
                index={
                    "dims": 1024,
                    "embed": get_default_embedding_model(),
                },
            ) as store,
            AsyncPostgresSaver.from_conn_string(DB_URI) as checkpointer,
        ):

            # 2. 测试语义搜索
            print("\n🔍 测试语义搜索功能")
            queries = [
                "主角的身份和血脉",
                "神界和凡界的区别",
                "修炼境界如何提升",
                "时空之道的掌握方法",
                "世界的力量体系",
            ]

            for query in queries:
                print("start" + "#" * 100)
                print(f"query: {query}")
                search_results = await store_asearch(
                    store,
                    ("book_app", "book_id_test"),
                    query=query,
                    limit=3,
                )
                for search_result in search_results:
                    print(f"search_result: {search_result}")
                    assert search_result.score != None
                    print("-" * 100)
                print("end" + "#" * 100)
    except Exception as e:
        logging.error(f"Error in store_test: {e}", exc_info=True)
        raise


if __name__ == "__main__":
    # ali_dashscope_embedding_test()
    # ali_openai_embedding_test()
    asyncio.run(store_test())
    asyncio.run(query_test())
    pass
