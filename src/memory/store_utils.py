from contextlib import asynccontextmanager
from typing import Optional

from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.config import get_store
from langgraph.store.postgres.aio import AsyncPostgresStore
from src.common.llms import get_default_embedding_model

# 数据库连接URI
DB_URI = "postgresql://postgres:postgres@localhost:5432/qflow?sslmode=disable"


@asynccontextmanager
async def get_store_and_checkpointer():
    """
    异步上下文管理器，用于初始化和管理AsyncPostgresStore和AsyncPostgresSaver

    Returns:
        tuple: (store, checkpointer) 元组
    """
    async with (
        AsyncPostgresStore.from_conn_string(
            DB_URI,
            index={
                "dims": 1024,
                "embed": get_default_embedding_model(),
            },
        ) as store,
        AsyncPostgresSaver.from_conn_string(DB_URI) as checkpointer,
    ):
        # 设置store和checkpointer
        await store.setup()
        await checkpointer.setup()

        yield store, checkpointer


def store_split_content_to_docs(
    long_content: str,
    metadata: dict,
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
) -> list[Document]:
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
    chunks = text_splitter.split_text(long_content)
    docs = text_splitter.create_documents(chunks, metadatas=[metadata] * len(chunks))
    return docs


async def store_aput(
    namespace: tuple[str, ...],
    key: str,
    content: str,
    store: Optional[AsyncPostgresStore] = None,
    metadata: dict = {},
    extra_value: dict = {},
    index: list[str] = [], # ["content"]
):
    if store is None:
        store = get_store()

    res = await store.aput(
        namespace=namespace,
        key=key,
        value={"content": content, "metadata": metadata, **extra_value},
        index=index,
    )
    return res


async def store_aget(
    namespace: tuple[str, ...],
    key: str,
    store: Optional[AsyncPostgresStore] = None,
):
    if store is None:
        store = get_store()
    res = await store.aget(
        namespace=namespace,
        key=key,
    )
    return res


async def store_asearch(
    store: Optional[AsyncPostgresStore],
    namespace_prefix: tuple[str, ...],
    query: str,
    limit: int = 5,
):
    if store is None:
        store = get_store()
    res = await store.asearch(
        namespace_prefix,  # namespace_prefix as positional argument
        query=query,
        limit=limit,
    )
    return res


__all__ = [
    "get_store_and_checkpointer",
    "store_split_content_to_docs",
    "store_aput",
    "store_aget",
    "store_asearch",
]
