import logging
import typing
from collections.abc import AsyncIterator
from typing import Any, List, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage

from langchain_core.runnables.utils import Output
from src.common.llms import get_llm
from src.common.utils.message_utils import MessageUtils
from src.common.utils.token_utils import TokenTools

logger = logging.getLogger(__name__)


async def get_response_from_astream(chunks: AsyncIterator[Output], callback=None) -> Output:
    first = True
    async for chunk in chunks:
        if first:
            response = chunk
            first = False
        else:
            response = response + chunk
        if callback:
            callback(chunk)

    if type(response) == dict:
        return response

    # 给glm4.5适配
    ori_content = response.content
    if "</think>" in ori_content:
        # 找到最后一个</think>，前后分为 part1 和 part2
        last_think_index = ori_content.rfind("</think>")
        part1 = ori_content[:last_think_index]
        part2 = ori_content[last_think_index + len("</think>") :]

        response.content = part2
        response.additional_kwargs["reasoning_content"] = part1.replace("<think>", "").replace("</think>", "")

    return response


async def llm_structured_request(
    node_name: str,
    input_messages: List[BaseMessage],
    schema_type: Union[typing.Dict, type],
    llm_params: dict[str, Any] = {},
    retry_index=0,
) -> tuple[
    AIMessage,
    Optional[Union[typing.Dict, type]],
    Optional[BaseException],
]:
    approximately_input_tokens = TokenTools.count_messages_approximately_tokens(input_messages)
    logger.info(
        f"[common_llm_structured_request][{node_name}] approximately_input_tokens: {approximately_input_tokens}"
    )
    # json mode 不能用astream
    # 报错response = response + chunk
    # TypeError: unsupported operand type(s) for +: 'WriterOutlineSchema' and 'WriterOutlineSchema'
    try:
        response = (
            await get_llm(node_name, llm_params)
            .with_structured_output(schema_type, method="json_mode", include_raw=True)
            .ainvoke(input_messages)
        )
    except Exception as e:
        logger.error(f"[common_llm_structured_request][{node_name}] ❌请求失败ainvoke,{e=}")
        try:
            response = await get_response_from_astream(
                get_llm(node_name, llm_params)
                .with_structured_output(schema_type, method="json_mode", include_raw=True)
                .astream(
                    input_messages,
                )
            )
        except Exception as e:
            logger.error(f"[common_llm_structured_request][{node_name}] ❌请求失败astream,{e=}")
            raise e
    # 解析输出
    logger.info(f"[common_llm_structured_request][{node_name}] ✅请求成功，type(response)={type(response)}")
    response_raw, response_parsed, response_error = MessageUtils.parse_include_raw_response(response)

    # 初始化token统计
    input_tokens = 0
    output_tokens = 0

    if response_raw.usage_metadata is not None:
        input_tokens = response_raw.usage_metadata.get("input_tokens", 0)
        output_tokens = response_raw.usage_metadata.get("output_tokens", 0)
        TokenTools.fit_to_coefficient(input_messages, input_tokens)
        logger.info(f"[common_llm_structured_request][{node_name}] ✅ usage_metadata={response_raw.usage_metadata}")
        logger.info(
            f"[common_llm_structured_request][{node_name}] ✅ input_tokens={input_tokens}, output_tokens={output_tokens}"
        )
    else:
        logger.warning(f"[common_llm_structured_request][{node_name}] response 未包含 token 用量信息")

    if response_parsed is None:
        if response_error is None:
            logger.error(
                f"[common_llm_structured_request][{node_name}]❌ 响应解析失败, response_parsed=None, response_error=None, {len(response_raw.content)=}"
            )
        else:
            logger.error(
                f"[common_llm_structured_request][{node_name}] ❌失败原因： {response_error=}, {len(response_raw)=}"
            )
        if retry_index < 2:
            logger.error(f"[common_llm_structured_request][{node_name}] 重试，正在第 {retry_index} 次重试")
            return await llm_structured_request(
                node_name, input_messages, schema_type, llm_params=llm_params, retry_index=retry_index + 1
            )
        else:
            logger.error(f"[common_llm_structured_request][{node_name}] ❌请求失败： {response['raw'].content=}")
            raise response_error

    # 返回token统计信息
    return response_raw, response_parsed, response_error


async def llm_str_request(
    node_name: str, input_messages: List[BaseMessage], llm_params: dict[str, Any] = {}, callback=None
) -> tuple[str, str]:
    approximately_input_tokens = TokenTools.count_messages_approximately_tokens(input_messages)
    logger.info(f"[{node_name}] approximately_input_tokens: {approximately_input_tokens}")

    response = await get_response_from_astream(
        get_llm(node_name).astream(input_messages),
        callback=lambda x: print(x.content, end="", flush=True),
    )

    response_content = response.content
    response_cot = response.additional_kwargs.get("reasoning_content", "")

    logger.info(
        f"[{node_name}] response.content.len={len(response_content)}, cot.len={len(response_cot)}, token:{response.usage_metadata}"
    )
    if response.usage_metadata is not None:
        input_tokens = (
            response.usage_metadata.get("input_tokens", -1) if hasattr(response.usage_metadata, "get") else None
        )
        TokenTools.fit_to_coefficient(input_messages, input_tokens)

    return response_content, response_cot
