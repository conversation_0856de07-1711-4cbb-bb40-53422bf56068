from pathlib import Path

from langgraph.config import get_config


class FileSystemUtils:
    @staticmethod
    def get_workspace_path() -> Path:
        path = get_config()["configurable"].get("workspace_path", Path.cwd() / "workspace")
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
        return path

    @staticmethod
    def write_content_to_workspace(relative_file_path: str, content: str, mode: str = "w") -> None:
        target_path = FileSystemUtils.get_workspace_path() / relative_file_path
        if not target_path.parent.exists():
            target_path.parent.mkdir(parents=True, exist_ok=True)
        with open(target_path, "w") as f:
            f.write(content)

    @staticmethod
    def get_full_path(relative_file_path: str) -> Path:
        target_path = FileSystemUtils.get_workspace_path() / relative_file_path
        return target_path

    @staticmethod
    def delete_file_from_workspace(relative_file_path: str) -> None:
        target_file_path = FileSystemUtils.get_workspace_path() / relative_file_path
        if target_file_path.exists():
            target_file_path.unlink()

    @staticmethod
    def read_content_from_workspace(relative_file_path: str) -> str:
        with open(FileSystemUtils.get_workspace_path() / relative_file_path, "r") as f:
            content = f.read()
            return content
