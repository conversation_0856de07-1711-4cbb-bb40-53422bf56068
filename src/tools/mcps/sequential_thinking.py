import logging


logger = logging.getLogger(__name__)

from langchain_mcp_adapters.client import MultiServerMCPClient

client = MultiServerMCPClient(
    {
        "sequential-thinking": {
            "command": "npx",
            "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
            "transport": "stdio",
        },
    }
)


mcp_tools = {}


async def init_mcp_tools():
    return
    logger.info("init_mcp_tools")
    tools = await client.get_tools()
    logger.info("init_mcp_tools done")
    mcp_tools["sequential-thinking"] = tools[0]


def get_sequential_thinking_tool():
    return mcp_tools["sequential-thinking"]
