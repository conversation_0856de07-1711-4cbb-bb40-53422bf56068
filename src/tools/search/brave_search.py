import json
import logging
import os

from langchain_community.tools import <PERSON><PERSON>ear<PERSON>, DuckDuckGoSearchResults
from langchain_community.tools.arxiv import ArxivQueryRun
from langchain_community.utilities import Arxiv<PERSON><PERSON><PERSON>rapper, BraveSearchWrapper

from src.tools.decorators import create_logged_tool

logger = logging.getLogger(__name__)


LoggedBraveSearch = create_logged_tool(BraveSearch)
brave_search_tool = LoggedBraveSearch(
    name="web_search",
    search_wrapper=BraveSearchWrapper(
        api_key=os.getenv("BRAVE_SEARCH_API_KEY", ""),
        search_kwargs={"count": 5},
    ),
)
