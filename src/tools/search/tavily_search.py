"""This module provides example tools for web scraping and search functionality.

It includes a basic Tavily search function (as an example)

These tools are intended as free examples to get started. For production use,
consider implementing more robust and specialized tools tailored to your needs.
"""

from typing import Any, cast, Optional

from langchain_core.tools import tool

from langchain_tavily import TavilySearch


@tool
async def tavily_search(query: str) -> Optional[dict[str, Any]]:
    """Search for general web results.

    This function performs a search using the Tavily search engine, which is designed
    to provide comprehensive, accurate, and trusted results. It's particularly useful
    for answering questions about current events.
    """
    wrapped = TavilySearch(max_results=5, tavily_api_key="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")
    return cast(dict[str, Any], await wrapped.ainvoke({"query": query}))
