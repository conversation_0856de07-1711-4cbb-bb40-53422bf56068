import json
import logging
import os

from langchain_community.tools import <PERSON><PERSON><PERSON><PERSON>, DuckDuckGoSearchResults
from langchain_community.tools.arxiv import ArxivQueryRun
from langchain_community.utilities import Arxiv<PERSON><PERSON><PERSON>rap<PERSON>, BraveSearchWrapper

from src.tools.decorators import create_logged_tool

logger = logging.getLogger(__name__)


LoggedDuckDuckGoSearch = create_logged_tool(DuckDuckGoSearchResults)
duckduckgo_search = LoggedDuckDuckGoSearch(name="duckduckgo_search", max_results=5)
