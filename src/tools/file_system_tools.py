import uuid
from datetime import datetime
from pathlib import Path
from tempfile import TemporaryDirectory
from typing import Union

from langchain_community.agent_toolkits import FileManagementToolkit
from langchain_community.tools.file_management.copy import CopyFileTool
from langchain_community.tools.file_management.delete import DeleteFileTool
from langchain_community.tools.file_management.file_search import FileSearchTool
from langchain_community.tools.file_management.list_dir import ListDirectoryTool
from langchain_community.tools.file_management.move import MoveFileTool
from langchain_community.tools.file_management.read import ReadFileTool
from langchain_community.tools.file_management.write import WriteFileTool
from langchain_core.tools import BaseTool

if __name__ == "__main__":
    import sys

    sys.path.append(str(Path(__file__).parent.parent.parent))

from src.common.utils.path_utils import get_workspace_root
from src.common.utils.time_utils import get_str_timestamp


def get_file_system_tools(workspace_path: Path = TemporaryDirectory()) -> Union[BaseTool, list[BaseTool]]:
    copy_file_tool = CopyFileTool(root_dir=str(workspace_path))
    delete_file_tool = DeleteFileTool(root_dir=str(workspace_path))
    file_search_tool = FileSearchTool(root_dir=str(workspace_path))
    move_file_tool = MoveFileTool(root_dir=str(workspace_path))
    read_file_tool = ReadFileTool(root_dir=str(workspace_path))
    write_file_tool = WriteFileTool(root_dir=str(workspace_path))
    list_directory_tool = ListDirectoryTool(root_dir=str(workspace_path))
    return [
        copy_file_tool,
        delete_file_tool,
        file_search_tool,
        move_file_tool,
        read_file_tool,
        write_file_tool,
        list_directory_tool,
    ]


__all__ = [
    "get_file_system_tools",
    "CopyFileTool",
    "DeleteFileTool",
    "FileSearchTool",
    "ListDirectoryTool",
    "MoveFileTool",
    "ReadFileTool",
    "WriteFileTool",
]


if __name__ == "__main__":
    # 以当前时间命名
    timestamp = get_str_timestamp()
    uuidstr = str(uuid.uuid4())
    workspace_path = get_workspace_root(sub_dir=f"test/{timestamp}_{uuidstr}")
    tools = get_file_system_tools(workspace_path)
    # ------ common test code ------
    write_file_tool = next(tool for tool in tools if type(tool) == WriteFileTool)
    print(write_file_tool.invoke({"file_path": "example.txt", "text": "Hello World!"}))
    copy_file_tool = next(tool for tool in tools if type(tool) == CopyFileTool)
    print(copy_file_tool.invoke({"source_path": "example.txt", "destination_path": "example_copy.txt"}))
    delete_file_tool = next(tool for tool in tools if type(tool) == DeleteFileTool)
    print(delete_file_tool.invoke({"file_path": "example.txt"}))

    file_search_tool = next(tool for tool in tools if type(tool) == FileSearchTool)
    print(file_search_tool.invoke({"dir_path": ".", "pattern": "example*"}))

    move_file_tool = next(tool for tool in tools if type(tool) == MoveFileTool)
    print(move_file_tool.invoke({"source_path": "example_copy.txt", "destination_path": "example_move.txt"}))

    read_file_tool = next(tool for tool in tools if type(tool) == ReadFileTool)
    print(read_file_tool.invoke({"file_path": "example_move.txt"}))

    print(write_file_tool.invoke({"file_path": "subfolder/example.txt", "text": "Hello World2!"}))
    print(write_file_tool.invoke({"file_path": "subfolder2/example.txt", "text": "Hello World2!"}))

    list_directory_tool = next(tool for tool in tools if type(tool) == ListDirectoryTool)
    print(list_directory_tool.invoke({"dir_path": "."}))
    # ------ common test code ------

    print(write_file_tool.invoke({"file_path": "s/example.txt", "text": "Hello World2!"}))
