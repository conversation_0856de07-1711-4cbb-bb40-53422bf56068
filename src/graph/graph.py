"""Define a custom Reasoning and Action agent.

Works with a chat model with tool calling support.
"""

import asyncio
from contextlib import asynccontextmanager

from langgraph.graph import StateGraph
from src.memory.store_utils import *
from src.nodes.book_setting.bs_planner_node import bs_planner_node
from src.nodes.book_setting.bs_steps_node import bs_refine_node, bs_step_condition_edge, bs_steps_node
from src.nodes.character.character_node import writer_character_node
from src.nodes.chatper.chapter_node import chapter_node

# 导入人工审核模块
from src.nodes.human_review.human_review_node import human_review_node
from src.nodes.outlines.outline_node import writer_outline_node

# 导入进度恢复相关模块
from src.nodes.progress.progress_node import progress_check_node
from src.nodes.scene.scene_node import writer_scene_node
from src.state import ConfigSchema, State

from src.tools.mcps.sequential_thinking import init_mcp_tools


@asynccontextmanager
async def qflow_graph():
    """
    创建并返回QFlow主图的异步上下文管理器
    """
    async with get_store_and_checkpointer() as (store, checkpointer):
        # 研究团队
        init_mcp_task = asyncio.create_task(init_mcp_tools())

        writer_team_graph = (
            StateGraph(State, config_schema=ConfigSchema)
            .set_entry_point("progress_check")  # 设置进度检查为入口点
            .add_node("progress_check", progress_check_node)  # 添加进度检查节点
            .add_node("bs_planner", bs_planner_node)
            .add_node("bs_steps", bs_steps_node)
            .add_node("bs_refine", bs_refine_node)
            .add_node("human_review", human_review_node)  # 添加人工审核节点
            .add_node("writer_character", writer_character_node)  # 添加角色设计节点
            .add_node("writer_outline", writer_outline_node)  # 添加大纲节点
            .add_node("writer_scene", writer_scene_node)  # 添加场景设计节点
            .add_node("chapter", chapter_node)
            .add_conditional_edges("bs_steps", bs_step_condition_edge)
            .set_finish_point("chapter")  # 设置写作节点为结束点
            .compile(name="QFlowAgent", checkpointer=checkpointer, store=store)
        )

        await init_mcp_task
        yield writer_team_graph


"""
    docker run --name qflow_agent_db \
        -e POSTGRES_USER=postgres \
        -e POSTGRES_PASSWORD=postgres \
        -e POSTGRES_DB=qflow \
        -d -p 5432:5432 postgres
# usage:
# graph = builder.compile(checkpointer=checkpointer, store=store)
# store.aput(namespace, str(uuid.uuid4()), {"data": memory})
# store.asearch(namespace, query=query)
"""
