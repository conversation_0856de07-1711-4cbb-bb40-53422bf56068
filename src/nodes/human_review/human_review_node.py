import logging
from typing import Optional

from langchain_core.messages import HumanMessage
from langchain_core.prompts import PromptTemplate
from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.common.llms import get_llm
from src.common.utils.filesystem_utils import FileSystemUtils
from src.common.utils.llm_request_utils import get_response_from_astream, llm_str_request, llm_structured_request
from src.common.utils.token_utils import TokenTools

from src.nodes.book_setting.bs_planner_node import BSPlannerNode
from src.nodes.book_setting.bs_steps_node import BSRefineNode, BSStepsNode
from src.nodes.character.character_node import WriterCharacterNode
from src.nodes.chatper.chapter_node import ChapterNode
from src.nodes.common.base_node import LLMJsonNode
from src.nodes.common.book_types import HumanReviewParams
from src.nodes.scene.scene_node import WriterSceneNode
from src.state import State

from ..progress.progress_manager import StateHistoryManager

logger = logging.getLogger(__name__)


node_map = {
    "bs_planner": BSPlannerNode,  # 书籍设定规划节点
    "bs_steps": BSStepsNode,  # 书籍设定步骤节点
    "bs_refine": BSRefineNode,  # 书籍设定精炼节点
    "character": WriterCharacterNode,  # 角色设计节点
    "chapter": ChapterNode,  # 章节生成节点
    "scene": WriterSceneNode,  # 场景设计节点
    # 注意：progress节点是FlowControlNode，不需要LLM响应，因此不包含在human_review中
    # 注意：outline节点还未完全实现，暂时不包含在human_review中
}

review_prompt_template = PromptTemplate(
    template="""
这是当前内容：

{review_params.review_content}

用户反馈和修改建议：

{user_input}

请根据以上用户建议，对内容进行修改和改进。
""",
    input_variables=["review_params", "user_input"],
)


class HumanReviewNode:
    def __init__(self, state: State, config: RunnableConfig):
        self.state = state
        self.config = config
        self.logger = logger
        self.node_name = "human_review"

        # 获取审核参数
        self.review_params: HumanReviewParams = state.get("human_review_params")
        assert self.review_params is not None

        self.full_path = FileSystemUtils.get_workspace_path() / self.review_params.review_file_name

    async def execute(self) -> Command:
        """执行人工审核流程"""
        self.logger.info("Human review node is executing.")
        self.logger.info(f'[{self.node_name}] Starting review for file: "{self.full_path}"')

        while True:
            # 获取用户输入
            print("\n请审核以下内容：")
            print(f'"{self.full_path}')
            print("- 输入 '/pass' 通过审核")
            print("- 输入修改建议，AI将根据建议重新生成：", end="")

            user_input = input().strip()

            if user_input.lower() == "/pass":
                return await self._handle_pass()
            elif user_input:
                try:
                    return await self._handle_user_feedback(user_input)
                except Exception as e:
                    self.logger.error(f"[{self.node_name}] Error regenerating content: {e}")
                    print(f"❌ 重新生成失败: {str(e)}")
                    print("请重新输入修改建议或输入 '/pass' 跳过")
                    continue
            else:
                print("⚠️ 输入不能为空，请重新输入")
                continue

    async def _handle_pass(self) -> Command:
        """处理审核通过的情况"""
        # 清理审核参数，跳转到下一个节点
        state_update = {"human_review_params": None}  # 标记审核通过，下次启动进入下一步
        await StateHistoryManager.dump_state({**self.state, **state_update}, metadata={"tag": "human_review"})
        self.logger.info(f"[{self.node_name}] Human review completed")
        return Command(update=state_update, goto=self.review_params.next_node)

    async def _handle_user_feedback(self, user_input: str) -> Command:
        """处理用户反馈，重新生成内容"""
        feedback_message = HumanMessage(
            name="review",
            content=review_prompt_template.format(review_params=self.review_params, user_input=user_input),
        )
        input_messages = [*self.review_params.input_messages, feedback_message]

        node = node_map[self.review_params.node_name](self.state, self.config)

        # 使用新的架构方法处理响应
        if node.schema_type is None:
            # 处理文本响应
            response_content, response_cot = await llm_str_request(
                self.review_params.node_name,
                input_messages,
                callback=lambda x: print(x.content, end="", flush=True),
            )
            command = await node.handle_text_response(response_content)
        else:
            # 处理结构化响应
            response_raw, response_parsed, response_error = await llm_structured_request(
                self.review_params.node_name, input_messages, node.schema_type
            )
            command = await node.handle_structured_response(response_raw, response_parsed, response_error)

        # 更新状态和review信息
        if command.update:
            # 更新状态
            self.state.update(command.update)

            # 获取新的review信息
            review_file_name, review_content = await node.get_review_info(command.update)
            if review_content:
                self.review_params.review_content = review_content

            # 保存更新后的review_params到状态
            updated_state = {"human_review_params": self.review_params}

            # 继续循环review而不是跳转到下一个节点
            print(f"\n✅ 内容已更新并保存")
            print(f"📁 文件路径: {self.full_path}")

            # 返回到循环开始，继续review
            return await self.execute()

        return command


async def human_review_node(state: State, config: RunnableConfig) -> Command:
    """
    通用人工审核节点

    用户可以输入：
    - /pass: 跳过审核，直接通过
    - 其他内容: 作为修改建议传给大模型重新生成
    """
    # 使用新的面向对象实现
    return await HumanReviewNode(state, config).execute()


__all__ = ["HumanReviewNode", "human_review_node"]
