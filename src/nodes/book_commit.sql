# 查看所有commit相关
select * from store where prefix like 'writer_app%commit%'



# 删除key为 c83eac28-215f-4234-b74a-d6d2461fef78 的数据
delete from store where prefix = 'writer_app.commit.head_hash' and key = 'd014d5ab-3217-4657-b2db-c8c8f99ee483';
delete from store where prefix = 'writer_app.commit.state.d014d5ab-3217-4657-b2db-c8c8f99ee483';



# 查看当前head
select * from store where prefix = 'writer_app.commit.head_hash' and key = 'c83eac28-215f-4234-b74a-d6d2461fef78';
select value::json->'content' as head,* from store where prefix = 'writer_app.commit.head_hash' and key = 'c83eac28-215f-4234-b74a-d6d2461fef78';


# 查看某bookid的所有提交
select value::json->'metadata' as metadata,* from store where prefix = 'writer_app.commit.state.c83eac28-215f-4234-b74a-d6d2461fef78' order by created_at desc



# ————————————————————————————————————————————————————————————进度调整————————————————————————————————————————————————————————————
# head-设定集完成-准备生成角色设定
update store set value = jsonb_set(value::jsonb, '{content}', '"a2a512d08d0efe0936c4411347ef903d629a6ed0a677cd62de83e0d9e85a753f"') 
where prefix = 'writer_app.commit.head_hash' and key = 'c83eac28-215f-4234-b74a-d6d2461fef78';


# head-角色简介列表，还没有角色明细
update store set value = jsonb_set(value::jsonb, '{content}', '"fbb28edbe75d554fb87e782453bef9e900b7ad8cb6c35174e7ced92c6694958c"') 
where prefix = 'writer_app.commit.head_hash' and key = 'c83eac28-215f-4234-b74a-d6d2461fef78';

# head-开始生成大纲
update store set value = jsonb_set(value::jsonb, '{content}', '"e7c6f2055b710bac5ca8fa7c185ca6c7459f1ed674d853a40f0cae77c720c50e"') 
where prefix = 'writer_app.commit.head_hash' and key = 'c83eac28-215f-4234-b74a-d6d2461fef78';

# head-开始细化大纲
update store set value = jsonb_set(value::jsonb, '{content}', '"8147d17700e7cfdae76035c2f0aaa39df19c5c5eb8efd35bf176d038f7704be6"') 
where prefix = 'writer_app.commit.head_hash' and key = 'c83eac28-215f-4234-b74a-d6d2461fef78';

