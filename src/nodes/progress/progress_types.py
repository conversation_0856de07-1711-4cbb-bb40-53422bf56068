import logging
from datetime import datetime
from typing import Literal, Optional

from pydantic import BaseModel, Field

logger = logging.getLogger(__name__)


class StateHistory(BaseModel):
    state: dict
    state_id: str
    created_at: datetime
    updated_at: datetime


class ProjectSelectionResult(BaseModel):
    """项目选择结果"""

    action: Literal["resume", "new"] = Field(..., description="操作类型：resume恢复项目，new新建项目")
    state_history: Optional[StateHistory] = Field(default=None, description="选中的状态")


__all__ = ["StateHistory", "ProjectSelectionResult"]
