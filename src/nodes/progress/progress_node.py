import logging
from typing import Annotated, Any, Dict, Literal, Optional, Sequence, TypedDict

from langchain_core.messages import HumanMessage, RemoveMessage
from langgraph.config import get_store, RunnableConfig
from langgraph.graph.message import REMOVE_ALL_MESSAGES
from langgraph.prebuilt import InjectedState
from langgraph.types import Command
from src.state import State
from src.nodes.common.base_node import FlowControlNode

from ..common.book_types import (
    BookDetail,
    BookSceneDesign,
    CharacterDetailCollection,
    CharacterSummaryCollection,
    HumanReviewParams,
    WriterPlan,
)

from .progress_manager import StateHistoryManager

logger = logging.getLogger(__name__)


class ProgressCheckNode(FlowControlNode):
    def __init__(self, state: Annotated[State, InjectedState], config: RunnableConfig):
        super().__init__("progress_check", state, config)

    async def pre_execute_check(self) -> Optional[Command]:
        """执行前检查"""
        return None

    async def execute_control_logic(self) -> Command:
        """
        执行进度检查控制逻辑

        检查数据库中是否有现有的小说项目，如果有则让用户选择恢复哪个项目
        如果没有或用户选择新建，则继续原有流程

        Returns:
            更新后的状态，包含项目选择结果
        """
        self.logger.info("开始执行进度检查节点")

        try:
            # 获取存储实例
            print("🔍 正在扫描数据库中的小说项目...")
            states = await StateHistoryManager.load_all_states()
            StateHistoryManager.display_books(states)
            selection_result = StateHistoryManager.get_user_choice(states)

            command_list = []
            if selection_result.action == "resume":
                command_list = await self._handle_resume(selection_result)
            else:
                command_list = await self._handle_new_project()

            self.logger.info(f"进度检查完成，操作类型: {selection_result.action}")
            return command_list

        except Exception as e:
            self.logger.error(f"进度检查节点执行失败: {e}, 从 planner node 启动")
            return Command(goto="bs_planner")

    async def _handle_resume(self, selection_result) -> list:
        """处理恢复现有项目的情况"""
        state_history = selection_result.state_history
        state_id = state_history.state_id
        resume_state = state_history.state
        writer_current_plan: WriterPlan = resume_state.get("writer_current_plan")
        assert writer_current_plan is not None

        # 恢复现有项目
        self.logger.info(f"恢复项目: {writer_current_plan.book_name} (ID: {state_id})")

        command_list = []
        # 1. 恢复状态，忽略启动脚本传递的新消息
        command_list.append(Command(update={"messages": [RemoveMessage(id=REMOVE_ALL_MESSAGES)]}))

        # 2. 根据当前进度决定跳转的节点
        next_node = self._determine_next_node(resume_state, writer_current_plan)
        self.logger.info(f"根据当前进度，跳转到节点: {next_node}")
        command_list.append(Command(update={**resume_state}, goto=next_node))

        return command_list

    async def _handle_new_project(self) -> list:
        """处理创建新项目的情况"""
        self.logger.info("创建新项目")
        return [
            Command(
                update={
                    "messages": self.state.get("messages", []),
                },
                goto="bs_planner",
            )
        ]

    def _determine_next_node(self, resume_state: Dict[str, Any], writer_current_plan: WriterPlan) -> str:
        """
        根据当前状态确定下一个应该跳转的节点

        Args:
            resume_state: 恢复的状态
            writer_current_plan: 当前的策划案

        Returns:
            下一个节点名称
        """
        # 检查是否正在review内容
        human_review_params: Optional[HumanReviewParams] = resume_state.get("human_review_params", None)
        if human_review_params:
            self.logger.info("正在review内容，跳转到 human_review")
            return "human_review"

        # 1. 检查策划案是否完成
        if not writer_current_plan.is_all_step_completed():
            self.logger.info("策划案未完成，跳转到 bs_steps")
            return "bs_steps"

        # 2. 检查策划案是否细化完成
        if not writer_current_plan.is_all_step_refined():
            self.logger.info("策划案未完成细化，跳转到 bs_refine")
            return "bs_refine"

        # 3. 检查角色设计是否完成
        character_summaries: CharacterSummaryCollection = resume_state.get("character_summaries", None)
        character_details: CharacterDetailCollection = resume_state.get("character_details", None)

        if character_summaries is None or not character_summaries.characters:
            self.logger.info("角色简介未完成，跳转到 writer_character")
            return "writer_character"

        if character_details is None or len(character_details.characters) < len(character_summaries.characters):
            self.logger.info("角色详情未完成，跳转到 writer_character")
            return "writer_character"

        # 4. 基于当前章节进度检查大纲是否完成
        book_detail: BookDetail = resume_state.get("book_detail", None)
        if book_detail is None or not book_detail.volume_outlines:
            self.logger.info("大纲未开始，跳转到 writer_outline")
            return "writer_outline"

        # 获取当前章节编号
        current_chapter_number = self._get_current_chapter_number(book_detail)

        if not book_detail.is_outline_ready_for_writing(current_chapter_number):
            self.logger.info(f"当前章节进度({current_chapter_number})所需的大纲未完成，跳转到 writer_outline")
            return "writer_outline"

        # 5. 基于当前章节进度检查场景设计是否完成
        scene_design: BookSceneDesign = resume_state.get("scene_design", None)
        if scene_design is None or not scene_design.volume_scenes:
            self.logger.info("场景设计未开始，跳转到 writer_scene")
            return "writer_scene"

        if not scene_design.is_scene_ready_for_writing(current_chapter_number):
            self.logger.info(f"当前章节进度({current_chapter_number})所需的场景设计未完成，跳转到 writer_scene")
            return "writer_scene"

        # 6. 所有前置工作都完成，开始章节创作
        self.logger.info(f"当前章节进度({current_chapter_number})的所有前置工作已完成，跳转到 chapter")
        return "chapter"

    def _get_current_chapter_number(self, book_detail: Optional[BookDetail]) -> int:
        """
        获取当前章节编号

        Args:
            book_detail: 书籍详情

        Returns:
            int: 当前章节编号（从1开始）
        """
        if book_detail is None or not book_detail.chapters:
            return 1  # 如果还没有章节，当前是第1章
        return len(book_detail.chapters) + 1  # 已完成章节数 + 1


async def progress_check_node(
    state: Annotated[State, InjectedState], config: RunnableConfig
) -> Command[
    Literal["bs_planner", "bs_steps", "bs_refine", "writer_character", "writer_outline", "writer_scene", "chapter"]
]:
    return await ProgressCheckNode(state, config).execute()


# 为了向后兼容，保留旧的函数接口
def determine_next_node(resume_state: Dict[str, Any], writer_current_plan: WriterPlan) -> str:
    """
    根据当前状态确定下一个应该跳转的节点（兼容性函数）

    Args:
        resume_state: 恢复的状态
        writer_current_plan: 当前的策划案

    Returns:
        下一个节点名称
    """
    # 创建临时的 ProgressCheckNode 来处理逻辑
    temp_node = ProgressCheckNode(None, None)
    return temp_node._determine_next_node(resume_state, writer_current_plan)


def get_current_chapter_number(book_detail: Optional[BookDetail]) -> int:
    """
    获取当前章节编号（兼容性函数）

    Args:
        book_detail: 书籍详情

    Returns:
        int: 当前章节编号（从1开始）
    """
    # 创建临时的 ProgressCheckNode 来处理逻辑
    temp_node = ProgressCheckNode(None, None)
    return temp_node._get_current_chapter_number(book_detail)


__all__ = [
    "ProgressCheckNode",
    "progress_check_node",
    "determine_next_node",
    "get_current_chapter_number",
]
