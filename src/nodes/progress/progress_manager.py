import hashlib
import logging
from typing import List
from uuid import uuid4

from langgraph.config import get_config, get_store

from src.nodes.common.book_types import WriterPlan
from src.nodes.progress.progress_types import *
from src.memory.store_utils import *
from typing import Any, Dict, List, Optional, Union

from src.state import State

logger = logging.getLogger(__name__)
kStateRestoreNamespace_commit_state = ("writer_app", "commit", "state")
kStateRestoreNamespace_commit_head = ("writer_app", "commit", "head_hash")
kMetadataKeyParentHash = "parent_hash"


class StateHistoryManager:

    @classmethod
    async def dump_state(self, state: State, metadata: Optional[Dict] = {}):
        """
        将state中的数据存储到数据库
        """
        namespace_base = kStateRestoreNamespace_commit_state
        namespace_id = book_id = state.get("book_id", str(uuid4()))
        commit_namespace = namespace_base + (namespace_id,)
        # commit的内容、hash、父hash
        commit_content = State.serialize_state(state)
        commit_hash_key = hashlib.sha256(commit_content.encode()).hexdigest()
        parent_hash = await StateHistoryManager.get_current_head_hash(book_id)

        # 提交
        await store_aput(
            namespace=commit_namespace,
            key=commit_hash_key,
            content=commit_content,
            metadata={**metadata, kMetadataKeyParentHash: parent_hash if parent_hash else ""},
        )
        # 更新head
        head_namespace = kStateRestoreNamespace_commit_head
        head_key = book_id
        head_content = commit_hash_key
        await store_aput(namespace=head_namespace, key=head_key, content=head_content)

    @classmethod
    async def get_current_head_hash(self, book_id: str) -> str:
        """
        获取state的head_hash
        """
        namespace = kStateRestoreNamespace_commit_head
        key = book_id
        stored_info = await store_aget(namespace=namespace, key=key)
        return stored_info.value["content"] if stored_info else None

    @staticmethod
    async def load_all_states() -> List[StateHistory]:
        try:
            store = get_store()
            search_all_heads = await store.asearch(kStateRestoreNamespace_commit_head, limit=20)

            state_history_list = []
            for item in search_all_heads:
                book_id = item.key
                commit_namespace = kStateRestoreNamespace_commit_state + (book_id,)
                commit_hash_key = item.value["content"]
                commit_content = await store.aget(commit_namespace, commit_hash_key)
                serialize_content = commit_content.value["content"]
                state = State.deserialize_state(serialize_content)
                state_history_list.append(
                    StateHistory(state=state, state_id=book_id, created_at=item.created_at, updated_at=item.updated_at)
                )
            return state_history_list
        except Exception as e:
            logger.error(f"加载所有状态失败: {e}")
            return []

    @classmethod
    def display_books(self, state_history_list: List[StateHistory]) -> None:
        """
        在命令行中显示项目列表

        Args:
            projects: 项目信息列表
        """
        if not state_history_list:
            print("\n📚 暂无进行中的小说项目")
            return

        print("\n" + "=" * 80)
        print("📚 发现以下进行中的小说项目:")
        print("=" * 80)

        for i, item in enumerate(state_history_list, 1):
            state = item.state
            plan: WriterPlan = state.get("writer_current_plan")
            book_name = plan.book_name
            book_description = plan.book_description

            print(f"\n{i}. 【{book_name}】")
            print(f"   📖 简介: {book_description}")
            print(f"   📖 创建时间: {item.created_at}")
            print(f"   📖 更新时间: {item.updated_at}")

        print("\n" + "=" * 80)

    @classmethod
    def get_user_choice(self, state_history_list: List[StateHistory]):
        if not state_history_list:
            print("没有找到现有项目，将创建新项目...")
            return ProjectSelectionResult(action="new")

        while True:
            try:
                print(f"\n请选择操作:")
                print(f"0. 创建新的小说项目")
                for i, item in enumerate(state_history_list, 1):
                    state = item.state
                    plan: WriterPlan = state.get("writer_current_plan")
                    book_name = plan.book_name
                    print(
                        f"{i}. 恢复项目: {book_name}(创建时间：{item.created_at}，更新时间：{item.updated_at}, id: {item.state_id})"
                    )

                resume_book_id = get_config().get("configurable", {}).get("resume_book_id", None)
                if resume_book_id:
                    for i, item in enumerate(state_history_list, 1):
                        if item.state_id == resume_book_id:
                            print(
                                f" -- 根据 resume_book_id 自动选择: {item.state.get("writer_current_plan").book_name}"
                            )
                            return ProjectSelectionResult(action="resume", state_history=item)

                choice = input(f"\n请输入选择 (0-{len(state_history_list)}): ").strip()

                if choice == "0":
                    return ProjectSelectionResult(action="new")

                choice_num = int(choice)
                if 1 <= choice_num <= len(state_history_list):
                    return ProjectSelectionResult(action="resume", state_history=state_history_list[choice_num - 1])
                else:
                    print(f"无效选择，请输入 0-{len(state_history_list)} 之间的数字")
                    continue
            except ValueError as e:
                print(f"请输入有效的数字, 当前输入: {choice}, 错误信息: {e}")
                continue
            except KeyboardInterrupt:
                print("\n\n用户取消操作")
                return ProjectSelectionResult(action="new")
