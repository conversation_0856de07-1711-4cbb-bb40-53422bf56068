import logging
import typing
from abc import ABC, abstractmethod
from typing import Any, Dict, Literal, Optional, Tuple, Union

from langchain_core.messages import AIMessage, BaseMessage
from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.common.llms import get_llm
from src.common.utils.filesystem_utils import FileSystemUtils
from src.common.utils.llm_request_utils import get_response_from_astream, llm_str_request, llm_structured_request
from src.common.utils.token_utils import TokenTools
from src.state import State

from ..progress.progress_manager import StateHistoryManager
from .book_types import HumanReviewParams
from .store_manager import WriterStoreManager

logger = logging.getLogger(__name__)


class BaseNode(ABC):
    """
    所有节点的基类，提供统一的执行流程和通用功能
    """

    def __init__(
        self,
        node_name: str,
        state: State,
        config: RunnableConfig,
        schema_type: Optional[Union[typing.Dict, type]] = None,
    ):
        self.logger = logging.getLogger(f"{__name__}.{node_name}")
        self.node_name = node_name
        self.schema_type = schema_type
        self.state = state
        self.config = config

        # 初始化存储管理器
        self.book_id = state.get("book_id")
        self.book_name = state.get("book_name")
        if self.book_id:
            self.store_manager = WriterStoreManager(book_id=self.book_id)
        else:
            self.store_manager = None

    async def execute(self) -> Command:
        """
        统一的节点执行流程
        """
        try:
            self.logger.info(f"[{self.node_name}] 开始执行节点")

            # 1. 执行前检查
            check_result = await self.pre_execute_check()
            if check_result is not None:
                return check_result

            # 2. 生成输入消息
            self.input_messages = await self.generate_prompt()

            # 3. 执行LLM请求
            if self.schema_type is not None:
                response_raw, response_parsed, response_error = await llm_structured_request(
                    self.node_name,
                    self.input_messages,
                    self.schema_type,
                )
                result = await self.handle_structured_response(response_raw, response_parsed, response_error)
            else:
                response_content, response_cot = await llm_str_request(
                    self.node_name,
                    self.input_messages,
                    callback=lambda x: print(x.content, end="", flush=True),
                )
                result = await self.handle_text_response(response_content)

            # 4. 后处理
            result = await self.post_process(result)

            self.logger.info(f"[{self.node_name}] 节点执行完成")
            return result

        except Exception as e:
            self.logger.error(f"[{self.node_name}] 节点执行失败: {e}")
            return await self.handle_error(e)

    # ==================== 抽象方法 ====================

    @abstractmethod
    async def pre_execute_check(self) -> Optional[Command]:
        """
        执行前检查，子类必须实现
        返回None表示继续执行，返回Command则直接返回
        """
        pass

    @abstractmethod
    async def generate_prompt(self) -> list[BaseMessage]:
        """
        生成LLM输入提示，子类必须实现
        """
        pass

    @abstractmethod
    async def handle_structured_response(
        self,
        response_raw: AIMessage,
        response_parsed: Optional[Union[typing.Dict, type]],
        response_error: Optional[BaseException],
    ) -> Command:
        """
        处理结构化响应，子类必须实现（当schema_type不为None时）
        """
        pass

    @abstractmethod
    async def handle_text_response(self, response_content: str) -> Command:
        """
        处理文本响应，子类必须实现（当schema_type为None时）
        """
        pass

    # ==================== 通用方法 ====================

    async def post_process(self, result: Command) -> Command:
        """
        后处理逻辑，包括状态存档、human review检查等
        """
        if not hasattr(result, "update") or not hasattr(result, "goto"):
            return result

        state_update = result.update if result.update else {}
        next_node = result.goto

        # 获取审查信息
        file_name, review_content = await self.get_review_info(state_update)

        # 检查是否需要human review
        if file_name and review_content:
            state_update, next_node = self._check_human_review(state_update, next_node, file_name, review_content)

        # 状态存档
        if state_update:
            tag = self.get_dump_tag()
            if tag:
                await StateHistoryManager.dump_state({**self.state, **state_update}, metadata={"tag": tag})

        return Command(update=state_update, goto=next_node)

    def _check_human_review(
        self, state_update: dict, next_node: str, file_name: str, review_content: str
    ) -> Tuple[dict, str]:
        """
        检查是否需要human review
        """
        if self.config["configurable"].get("enable_human_review", False):
            old_human_review_params = self.state.get("human_review_params", None)
            if old_human_review_params is not None:
                input_messages = old_human_review_params.input_messages
            else:
                input_messages = self.input_messages

            human_review_params = HumanReviewParams(
                node_name=self.node_name,
                input_messages=input_messages,
                review_content=review_content,
                review_file_name=file_name,
                next_node=next_node,
            )
            state_update.update({"human_review_params": human_review_params})
            next_node = "human_review"
        return state_update, next_node

    async def get_review_info(self, state_update: dict) -> Tuple[Optional[str], Optional[str]]:
        """
        获取人工审查所需的文件名和内容
        子类可以重写此方法来提供具体的审查信息

        Returns:
            tuple: (file_name, review_content)
        """
        return None, None

    def get_dump_tag(self) -> str:
        """
        获取状态存档的标签
        子类可以重写此方法来提供具体的标签

        Returns:
            str: 标签名称
        """
        return self.node_name

    async def handle_error(self, error: Exception) -> Command:
        """
        统一的错误处理
        """
        self.logger.error(f"[{self.node_name}] 执行出错: {error}")
        # 默认返回错误状态，子类可以重写
        return Command(update={"error": str(error)}, goto="__end__")

    async def save_to_file(self, content: str, file_path: str) -> None:
        """
        保存内容到文件
        """
        if self.book_name and self.book_id:
            full_path = f"{self.book_name}/{self.book_id}/{file_path}"
            try:
                FileSystemUtils.write_content_to_workspace(full_path, content)
                self.logger.info(f"[{self.node_name}] 内容已保存到: {full_path}")
            except RuntimeError as e:
                if "Called get_config outside of a runnable context" in str(e):
                    self.logger.warning(f"[{self.node_name}] 无法保存文件（不在运行上下文中）: {full_path}")
                else:
                    raise

    async def save_to_database(self, data: Any, data_type: str) -> None:
        """
        保存数据到数据库
        """
        if self.store_manager:
            # 这里可以根据data_type调用不同的存储方法
            # 具体实现由子类决定
            pass

    # ==================== 统一的文件和数据库操作 ====================

    async def save_content_with_review(
        self, content: str, file_path: str, data_type: str = "content"
    ) -> Tuple[str, str]:
        """
        统一的内容保存方法，包含文件保存和review信息设置

        Returns:
            tuple: (review_file_name, review_content)
        """
        # 保存到文件
        await self.save_to_file(content, file_path)

        # 设置review信息
        review_file_name = f"{self.book_name}/{self.book_id}/{file_path}"
        review_content = content

        return review_file_name, review_content

    async def save_structured_data_with_review(
        self, data: Any, file_path: str, data_type: str = "structured_data"
    ) -> Tuple[str, str]:
        """
        统一的结构化数据保存方法，包含文件保存、数据库保存和review信息设置

        Returns:
            tuple: (review_file_name, review_content)
        """
        # 格式化内容
        if hasattr(data, "model_dump_json"):
            file_content = data.model_dump_json(indent=2)
        elif hasattr(data, "get_completed_contents"):
            file_content = data.get_completed_contents()
        elif hasattr(data, "__str__"):
            file_content = str(data)
        else:
            file_content = repr(data)

        # 保存到文件
        await self.save_to_file(file_content, file_path)

        # 保存到数据库
        await self.save_to_database(data, data_type)

        # 设置review信息
        review_file_name = f"{self.book_name}/{self.book_id}/{file_path}"
        review_content = file_content

        return review_file_name, review_content

    async def update_state_and_save(
        self,
        state_update: dict,
        content: str = None,
        file_path: str = None,
        data: Any = None,
        data_type: str = "content",
    ) -> Tuple[dict, Optional[str], Optional[str]]:
        """
        统一的状态更新和保存方法

        Returns:
            tuple: (state_update, review_file_name, review_content)
        """
        review_file_name = None
        review_content = None

        if content and file_path:
            # 保存文本内容
            review_file_name, review_content = await self.save_content_with_review(content, file_path, data_type)
        elif data and file_path:
            # 保存结构化数据
            review_file_name, review_content = await self.save_structured_data_with_review(data, file_path, data_type)

        return state_update, review_file_name, review_content


class LLMJsonNode(BaseNode):
    """
    兼容性类，保持向后兼容
    """

    def __init__(
        self,
        logger,
        node_name: str,
        schema_type: Union[typing.Dict, type],
        state: State,
        config: RunnableConfig,
    ):
        super().__init__(node_name, state, config, schema_type)
        # 保持原有的logger参数兼容性
        if logger:
            self.logger = logger

    async def pre_execute_check(self) -> Optional[Command]:
        """兼容性方法"""
        return await self.on_node_check()

    async def generate_prompt(self) -> list[BaseMessage]:
        """兼容性方法"""
        return self.on_need_prompt()

    async def handle_structured_response(
        self,
        response_raw: AIMessage,
        response_parsed: Optional[Union[typing.Dict, type]],
        response_error: Optional[BaseException],
    ) -> Command:
        """兼容性方法"""
        return await self.on_json_response(response_raw, response_parsed, response_error)

    async def handle_text_response(self, response_content: str) -> Command:
        """兼容性方法"""
        return await self.on_str_response(response_content)

    async def get_review_info(self, state_update: dict) -> Tuple[Optional[str], Optional[str]]:
        """兼容性方法"""
        return self.on_get_review_info(state_update)

    def get_dump_tag(self) -> str:
        """兼容性方法"""
        return self.on_get_dump_tag()

    # ==================== 兼容性方法 ====================

    def on_need_prompt(self):
        """子类需要实现此方法来生成prompt"""
        raise NotImplementedError("子类必须实现 on_need_prompt 方法")

    async def on_node_check(self):
        """节点执行前检查，子类可以重写此方法
        返回None表示继续执行，返回其他Command则直接返回
        """
        return None

    def on_get_review_info(self, state_update: dict):
        """
        获取人工审查所需的文件名和内容
        子类可以重写此方法来提供具体的审查信息

        Returns:
            tuple: (file_name, review_content)
        """
        return None, None

    def on_get_dump_tag(self):
        """
        获取状态存档的标签
        子类可以重写此方法来提供具体的标签

        Returns:
            str: 标签名称
        """
        return self.node_name


# ==================== 专用基类 ====================


class ContentGenerationNode(BaseNode):
    """
    内容生成节点基类，用于生成文本内容的节点
    """

    def __init__(self, node_name: str, state: State, config: RunnableConfig):
        super().__init__(node_name, state, config, schema_type=None)
        self._review_file_name = None
        self._review_content = None

    async def handle_structured_response(
        self,
        response_raw: AIMessage,
        response_parsed: Optional[Union[typing.Dict, type]],
        response_error: Optional[BaseException],
    ) -> Command:
        """内容生成节点不使用结构化响应"""
        raise NotImplementedError("内容生成节点应该使用文本响应")

    async def handle_text_response(self, response_content: str) -> Command:
        """
        处理文本响应的通用逻辑
        """
        # 1. 更新内容
        state_update = await self.update_content(response_content)

        # 2. 统一保存和设置review信息
        file_path = await self.get_file_path()
        if file_path:
            self._review_file_name, self._review_content = await self.save_content_with_review(
                response_content, file_path, "content"
            )

        # 3. 保存到数据库
        await self.save_content_to_database(response_content)

        # 4. 确定下一个节点
        next_node = await self.get_next_node()

        return Command(update=state_update, goto=next_node)

    async def get_review_info(self, state_update: dict) -> Tuple[Optional[str], Optional[str]]:
        """获取审查信息"""
        return self._review_file_name, self._review_content

    @abstractmethod
    async def update_content(self, content: str) -> dict:
        """更新状态中的内容，返回状态更新字典"""
        pass

    @abstractmethod
    async def get_file_path(self) -> str:
        """获取文件保存路径"""
        pass

    @abstractmethod
    async def save_content_to_database(self, content: str) -> None:
        """保存内容到数据库"""
        pass

    @abstractmethod
    async def get_next_node(self) -> str:
        """获取下一个节点名称"""
        pass


class StructuredDataNode(BaseNode):
    """
    结构化数据节点基类，用于生成JSON结构数据的节点
    """

    def __init__(self, node_name: str, state: State, config: RunnableConfig, schema_type: type):
        super().__init__(node_name, state, config, schema_type)
        self._review_file_name = None
        self._review_content = None

    async def handle_text_response(self, response_content: str) -> Command:
        """结构化数据节点不使用文本响应"""
        raise NotImplementedError("结构化数据节点应该使用结构化响应")

    async def handle_structured_response(
        self,
        response_raw: AIMessage,
        response_parsed: Optional[Union[typing.Dict, type]],
        response_error: Optional[BaseException],
    ) -> Command:
        """
        处理结构化响应的通用逻辑
        """
        if response_error:
            self.logger.error(f"[{self.node_name}] 结构化响应解析失败: {response_error}")
            return await self.handle_error(response_error)

        # 1. 更新数据
        state_update = await self.update_data(response_parsed)

        # 2. 统一保存和设置review信息
        file_path = await self.get_file_path()
        if file_path:
            self._review_file_name, self._review_content = await self.save_structured_data_with_review(
                response_parsed, file_path, "structured_data"
            )

        # 3. 保存到数据库
        await self.save_data_to_database(response_parsed)

        # 4. 确定下一个节点
        next_node = await self.get_next_node()

        return Command(update=state_update, goto=next_node)

    async def get_review_info(self, state_update: dict) -> Tuple[Optional[str], Optional[str]]:
        """获取审查信息"""
        return self._review_file_name, self._review_content

    @abstractmethod
    async def update_data(self, data: Any) -> dict:
        """更新状态中的数据，返回状态更新字典"""
        pass

    @abstractmethod
    async def get_file_path(self) -> str:
        """获取文件保存路径"""
        pass

    @abstractmethod
    async def format_content_for_file(self, data: Any) -> str:
        """格式化数据为文件内容"""
        pass

    @abstractmethod
    async def save_data_to_database(self, data: Any) -> None:
        """保存数据到数据库"""
        pass

    @abstractmethod
    async def get_next_node(self) -> str:
        """获取下一个节点名称"""
        pass


class FlowControlNode(BaseNode):
    """
    流程控制节点基类，用于控制执行流程的节点
    """

    def __init__(self, node_name: str, state: State, config: RunnableConfig):
        super().__init__(node_name, state, config, schema_type=None)

    async def generate_prompt(self) -> list[BaseMessage]:
        """流程控制节点通常不需要LLM"""
        return []

    async def handle_structured_response(
        self,
        response_raw: AIMessage,
        response_parsed: Optional[Union[typing.Dict, type]],
        response_error: Optional[BaseException],
    ) -> Command:
        """流程控制节点不使用LLM响应"""
        raise NotImplementedError("流程控制节点不应该使用LLM响应")

    async def handle_text_response(self, response_content: str) -> Command:
        """流程控制节点不使用LLM响应"""
        raise NotImplementedError("流程控制节点不应该使用LLM响应")

    async def execute(self) -> Command:
        """
        流程控制节点的简化执行流程
        """
        try:
            self.logger.info(f"[{self.node_name}] 开始执行流程控制")

            # 执行前检查
            check_result = await self.pre_execute_check()
            if check_result is not None:
                return check_result

            # 执行控制逻辑
            result = await self.execute_control_logic()

            # 后处理
            result = await self.post_process(result)

            self.logger.info(f"[{self.node_name}] 流程控制执行完成")
            return result

        except Exception as e:
            self.logger.error(f"[{self.node_name}] 流程控制执行失败: {e}")
            return await self.handle_error(e)

    @abstractmethod
    async def execute_control_logic(self) -> Command:
        """执行控制逻辑，子类必须实现"""
        pass


# ==================== 高级专用基类 ====================


class MultiStageNode(StructuredDataNode):
    """
    多阶段节点基类，用于有多个执行阶段的节点
    如character_node（init_character -> detailed_character）
    如outline_node（init_volume_outlines -> refine_volume_outlines）
    """

    def __init__(self, node_name: str, state: State, config: RunnableConfig):
        # 先确定阶段和schema
        self.stage = self._determine_stage(state)
        schema_type = self._get_schema_for_stage(self.stage)

        super().__init__(node_name, state, config, schema_type)

        # 初始化阶段相关数据
        self._init_stage_data()

    async def pre_execute_check(self) -> Optional[Command]:
        """执行前检查"""
        # 检查前置条件
        check_result = await self._check_prerequisites()
        if check_result is not None:
            return check_result

        # 检查是否已完成
        if self.stage == "completed":
            next_node = await self._get_completion_next_node()
            self.logger.info(f"[{self.node_name}] 所有阶段已完成，跳转到: {next_node}")
            return Command(goto=next_node)

        return None

    async def generate_prompt(self) -> list[BaseMessage]:
        """生成当前阶段的prompt"""
        return await self._generate_stage_prompt(self.stage)

    async def handle_structured_response(
        self,
        response_raw: AIMessage,
        response_parsed: Optional[Union[typing.Dict, type]],
        response_error: Optional[BaseException],
    ) -> Command:
        """处理结构化响应"""
        if response_error:
            self.logger.error(f"[{self.node_name}] 阶段{self.stage}响应解析失败: {response_error}")
            return await self.handle_error(response_error)

        return await self._handle_stage_response(self.stage, response_parsed)

    # ==================== 抽象方法 ====================

    @abstractmethod
    def _determine_stage(self, state: State) -> str:
        """确定当前应该执行哪个阶段"""
        pass

    @abstractmethod
    def _get_schema_for_stage(self, stage: str) -> Optional[type]:
        """根据阶段返回对应的schema"""
        pass

    @abstractmethod
    def _init_stage_data(self) -> None:
        """初始化阶段相关数据"""
        pass

    @abstractmethod
    async def _check_prerequisites(self) -> Optional[Command]:
        """检查前置条件"""
        pass

    @abstractmethod
    async def _get_completion_next_node(self) -> str:
        """获取完成后的下一个节点"""
        pass

    @abstractmethod
    async def _generate_stage_prompt(self, stage: str) -> list[BaseMessage]:
        """生成指定阶段的prompt"""
        pass

    @abstractmethod
    async def _handle_stage_response(self, stage: str, response_parsed: Any) -> Command:
        """处理指定阶段的响应"""
        pass

    # ==================== 通用方法 ====================

    async def update_data(self, data: Any) -> dict:
        """更新状态中的数据（委托给阶段处理方法）"""
        # 这个方法会被_handle_stage_response调用
        return {}

    async def get_file_path(self) -> str:
        """获取文件保存路径（委托给阶段处理方法）"""
        return ""

    async def format_content_for_file(self, data: Any) -> str:
        """格式化数据为文件内容（委托给阶段处理方法）"""
        return ""

    async def save_data_to_database(self, data: Any) -> None:
        """保存数据到数据库（委托给阶段处理方法）"""
        pass

    async def get_next_node(self) -> str:
        """获取下一个节点名称"""
        return self.node_name  # 默认继续当前节点，进入下一阶段


class IterativeProcessingNode(StructuredDataNode):
    """
    迭代处理节点基类，用于需要迭代处理多个项目的节点
    如character_node的详情生成、chapter_node的章节生成
    """

    def __init__(self, node_name: str, state: State, config: RunnableConfig, schema_type: type):
        super().__init__(node_name, state, config, schema_type)

        # 初始化迭代相关数据
        self._init_iteration_data()

    async def pre_execute_check(self) -> Optional[Command]:
        """执行前检查"""
        # 检查前置条件
        check_result = await self._check_prerequisites()
        if check_result is not None:
            return check_result

        # 检查是否已完成所有迭代
        if await self._is_all_iterations_completed():
            next_node = await self._get_completion_next_node()
            self.logger.info(f"[{self.node_name}] 所有迭代已完成，跳转到: {next_node}")
            return Command(goto=next_node)

        return None

    async def generate_prompt(self) -> list[BaseMessage]:
        """生成当前迭代的prompt"""
        current_item = await self._get_current_iteration_item()
        return await self._generate_iteration_prompt(current_item)

    async def handle_structured_response(
        self,
        response_raw: AIMessage,
        response_parsed: Optional[Union[typing.Dict, type]],
        response_error: Optional[BaseException],
    ) -> Command:
        """处理结构化响应"""
        if response_error:
            self.logger.error(f"[{self.node_name}] 迭代响应解析失败: {response_error}")
            return await self.handle_error(response_error)

        return await self._handle_iteration_response(response_parsed)

    # ==================== 抽象方法 ====================

    @abstractmethod
    def _init_iteration_data(self) -> None:
        """初始化迭代相关数据"""
        pass

    @abstractmethod
    async def _check_prerequisites(self) -> Optional[Command]:
        """检查前置条件"""
        pass

    @abstractmethod
    async def _is_all_iterations_completed(self) -> bool:
        """检查是否所有迭代都已完成"""
        pass

    @abstractmethod
    async def _get_completion_next_node(self) -> str:
        """获取完成后的下一个节点"""
        pass

    @abstractmethod
    async def _get_current_iteration_item(self) -> Any:
        """获取当前迭代的项目"""
        pass

    @abstractmethod
    async def _generate_iteration_prompt(self, current_item: Any) -> list[BaseMessage]:
        """生成当前迭代的prompt"""
        pass

    @abstractmethod
    async def _handle_iteration_response(self, response_parsed: Any) -> Command:
        """处理迭代响应"""
        pass

    # ==================== 通用方法 ====================

    async def update_data(self, data: Any) -> dict:
        """更新状态中的数据（委托给迭代处理方法）"""
        return {}

    async def get_file_path(self) -> str:
        """获取文件保存路径（委托给迭代处理方法）"""
        return ""

    async def format_content_for_file(self, data: Any) -> str:
        """格式化数据为文件内容（委托给迭代处理方法）"""
        return ""

    async def save_data_to_database(self, data: Any) -> None:
        """保存数据到数据库（委托给迭代处理方法）"""
        pass

    async def get_next_node(self) -> str:
        """获取下一个节点名称"""
        return self.node_name  # 默认继续当前节点，进行下一次迭代
