import asyncio
import inspect
import json
import logging
import typing
import warnings
from abc import ABC, abstractmethod
from collections.abc import AsyncIterator, Iterator, Sequence
from functools import cached_property
from operator import itemgetter
from typing import Any, Callable, cast, List, Literal, Optional, TYPE_CHECKING, Union

from langchain_core.messages import AIMessage, BaseMessage, SystemMessage
from langgraph.types import Command
from pydantic import Field
from src.common.llms import get_llm
from src.common.utils.filesystem_utils import FileSystemUtils
from src.common.utils.message_utils import MessageUtils
from src.common.utils.time_utils import get_prompt_timestamp

from src.nodes.common.book_types import BookDetail, CharacterDetailCollection, VolumeOutline, WriterPlan

from src.state import State
from src.nodes.outlines.outline_prompt import *
from langchain_core.load.serializable import Serializable
from langgraph.config import RunnableConfig
from src.common.utils.llm_request_utils import llm_structured_request
from src.common.utils.token_utils import TokenTools
from src.nodes.common.base_node import LLMJsonNode, MultiStageNode

logger = logging.getLogger(__name__)


def get_current_chapter_number(book_detail: Optional[BookDetail]) -> int:
    """
    获取当前章节编号

    Args:
        book_detail: 书籍详情

    Returns:
        int: 当前章节编号（从1开始）
    """
    if book_detail is None or not book_detail.chapters:
        return 1  # 如果还没有章节，当前是第1章
    return len(book_detail.chapters) + 1  # 已完成章节数 + 1


class WriterOutlineNode(MultiStageNode):
    def __init__(self, state: State, config: RunnableConfig):
        self.stage = self._determine_stage(state)
        schema_type = self._get_schema_for_stage(self.stage)

        super().__init__(
            logger=logger,
            node_name="writer_outline",
            schema_type=schema_type,
            state=state,
            config=config,
        )

        # 获取数据
        self.writer_current_plan: WriterPlan = state.get("writer_current_plan")
        self.character_details = state.get("character_details", None)
        self.character_summaries = state.get("character_summaries", None)
        self.book_detail: Optional[BookDetail] = state.get("book_detail", None)
        self.book_id = state.get("book_id")
        self.book_name = state.get("book_name")
        self.refined_book_setting = state.get("refined_book_setting", None)

        # 初始化大纲相关数据（原本在on_need_prompt中）
        self.current_chapter = get_current_chapter_number(self.book_detail)

        # 预计算细化阶段的相关信息
        if self.stage == "refine_volume_outlines":
            self._prepare_refinement_info_init()

    def _determine_stage(self, state: State) -> str:
        """确定当前应该执行哪个阶段"""
        book_detail = state.get("book_detail", None)
        current_chapter = get_current_chapter_number(book_detail)

        if book_detail is None or len(book_detail.volume_outlines) == 0:
            return "init_volume_outlines"
        elif book_detail.need_volume_outline_refinement_for_current_progress(current_chapter):
            return "refine_volume_outlines"
        else:
            return "completed"

    def _get_schema_for_stage(self, stage: str):
        """根据阶段返回对应的schema"""
        if stage == "init_volume_outlines":
            return BookVolumeOutlinesSchema
        elif stage == "refine_volume_outlines":
            return VolumeOutline
        else:
            return None

    async def on_node_check(self) -> Optional[Command]:
        """节点执行前检查"""
        # 前置步骤检查
        assert self.writer_current_plan.is_all_step_completed(), "❌ 策划案未完成，无法创建大纲"
        assert self.writer_current_plan.is_all_step_refined(), "❌ 策划案未完成细化，无法创建大纲"
        assert self.state.get("book_id") is not None, "book_id 为空"

        # 检查角色设计是否完成
        assert self.character_summaries is not None, "❌ 角色简介未完成，无法创建大纲"
        assert self.character_details is not None, "❌ 角色详情未完成，无法创建大纲"
        assert len(self.character_details.characters) == len(
            self.character_summaries.characters
        ), "❌ 角色详情未完全完成，无法创建大纲"

        # 检查阶段是否已完成并进行跳转
        if self.stage == "completed":
            # 需要先计算current_chapter用于日志
            current_chapter = get_current_chapter_number(self.book_detail)
            logger.info(f"当前章节进度({current_chapter})所需的大纲已完成，开始场景设计阶段")
            return Command(goto="writer_scene")

        return None

    def _prepare_refinement_info_init(self):
        """在__init__中准备细化阶段的相关信息"""
        # 基于当前章节进度获取下一个需要细化的卷索引
        current_volume_number = (self.current_chapter - 1) // 100 + 1  # 假设每卷100章
        max_required_volume = current_volume_number + 2  # 当前卷+后面2卷

        self.volume_index = None
        self.current_volume = None
        # 查找需要细化的卷
        if self.book_detail and self.book_detail.volume_outlines:
            for i, volume_outline in enumerate(self.book_detail.volume_outlines):
                if (
                    volume_outline.version == 0
                    and volume_outline.volume_number >= current_volume_number
                    and volume_outline.volume_number <= max_required_volume
                ):
                    self.volume_index = i
                    break

            if self.volume_index is not None:
                self.current_volume = self.book_detail.volume_outlines[self.volume_index]
                logger.info(
                    f"[初始化] 基于当前章节进度({self.current_chapter})，预备细化第 {self.current_volume.volume_number} 卷大纲"
                )

    def _prepare_refinement_info(self):
        """准备细化阶段的相关信息"""
        # 基于当前章节进度获取下一个需要细化的卷索引
        current_volume_number = (self.current_chapter - 1) // 100 + 1  # 假设每卷100章
        max_required_volume = current_volume_number + 2  # 当前卷+后面2卷

        self.volume_index = None
        # 查找需要细化的卷
        for i, volume_outline in enumerate(self.book_detail.volume_outlines):
            if (
                volume_outline.version == 0
                and volume_outline.volume_number >= current_volume_number
                and volume_outline.volume_number <= max_required_volume
            ):
                self.volume_index = i
                break

        if self.volume_index is not None:
            self.current_volume = self.book_detail.volume_outlines[self.volume_index]
            logger.info(
                f"[{self.node_name}] 基于当前章节进度({self.current_chapter})，开始细化第 {self.current_volume.volume_number} 卷大纲"
            )

    def on_need_prompt(self):
        """生成大纲创建的prompt"""
        # 数据已在__init__中初始化，直接使用
        if self.stage == "init_volume_outlines":
            logger.info(f"大纲阶段1，生成整书分卷大纲")
            return self._generate_init_volume_outlines_prompt()
        elif self.stage == "refine_volume_outlines":
            logger.info(f"大纲阶段2，基于当前章节进度({self.current_chapter})细化卷大纲")
            # 数据已在__init__中预备好，直接使用
            return self._generate_refine_volume_outlines_prompt()
        else:
            raise ValueError(f"Unknown stage: {self.stage}")

    def _generate_init_volume_outlines_prompt(self):
        """生成初始分卷大纲的prompt"""
        # 获取角色信息
        character_content = self.character_details.get_characters_content()

        # 提示词
        prompt_list = []
        volum_outlines_prompt = bo_init_prompt_template.format(
            json_schema=BookVolumeOutlinesSchema.model_json_schema(),
            CURRENT_TIME=get_prompt_timestamp(),
            book_name=self.writer_current_plan.book_name,
            book_description=self.writer_current_plan.book_description,
            refined_book_setting=self.refined_book_setting,
            character_design=character_content,
        )
        prompt_list.append(volum_outlines_prompt)

        # 添加demo章节
        demo_chapter = self.config["configurable"].get("demo_chapter", None)
        if demo_chapter is not None:
            demo_chapter_prompt = bo_demo_chapter_template.format(demo_chapter=demo_chapter)
            prompt_list.append(demo_chapter_prompt)

        prompt = "\n\n".join(prompt_list)
        input_messages = [SystemMessage(content=prompt)]
        return input_messages

    def _generate_refine_volume_outlines_prompt(self):
        """生成细化卷大纲的prompt"""
        if self.volume_index is None:
            logger.info(f"[{self.node_name}] 当前章节进度({self.current_chapter})所需的所有卷大纲已完成细化")
            # 这种情况应该在 on_node_check 中处理，但为了安全起见还是处理一下
            return None

        # 获取角色信息
        character_content = self.character_details.get_characters_content()

        # 准备提示词
        refined_book_outlines = self.book_detail.get_completed_volume_outlines()
        prompt = bo_detail_prompt_template.format(
            json_schema=VolumeOutline.model_json_schema(),
            refined_book_setting=self.refined_book_setting,
            character_design=character_content,
            refined_book_outlines=refined_book_outlines,
            volume_index=self.current_volume.volume_number,
        )
        if self.volume_index == len(self.book_detail.volume_outlines) - 1:
            prompt += "\n\n**最终卷的结局留有一点点的悬念，以便续作或同世界观新作的展开**"

        input_messages = [SystemMessage(content=prompt)]
        return input_messages

    async def on_json_response(
        self,
        response_raw: AIMessage,
        response_parsed: Optional[Union[typing.Dict, type]],
        response_error: Optional[BaseException],
    ) -> Command[Literal["writer_outline", "writer_scene"]]:
        """处理大纲创建响应"""
        if self.stage == "init_volume_outlines":
            return await self._handle_init_volume_outlines_response(response_parsed)
        elif self.stage == "refine_volume_outlines":
            return await self._handle_refine_volume_outlines_response(response_parsed)
        else:
            raise ValueError(f"Unknown stage: {self.stage}")

    async def _handle_init_volume_outlines_response(self, response_parsed) -> Command:
        """处理初始分卷大纲响应"""
        assert len(response_parsed.volume_outlines) > 0, "❌ 整书分卷大纲创建失败"

        # 1. 解析并处理响应
        book_detail = BookDetail(volume_outlines=response_parsed.volume_outlines)
        logger.info(f"[{self.node_name}] ✅整书分卷大纲创建响应已解析: len={len(book_detail.model_dump_json())}")

        # 2. 保存到本地文件
        file_path = f"{self.book_name}/{self.book_id}/outline/整书分卷大纲.md"
        FileSystemUtils.write_content_to_workspace(file_path, book_detail.get_completed_volume_outlines())
        logger.info(f"[{self.node_name}] ✅整书分卷大纲已保存到本地文件: {file_path}")

        # 3. 准备状态更新
        state_update = {"book_detail": book_detail}

        # 4. agent 下一步
        return Command(update=state_update, goto=self.node_name)

    async def _handle_refine_volume_outlines_response(self, response_parsed: VolumeOutline) -> Command:
        """处理细化卷大纲响应"""
        refined_volume: VolumeOutline = response_parsed
        # 更新版本号
        refined_volume.version = self.current_volume.version + 1
        refined_volume.volume_number = self.current_volume.volume_number
        refined_volume.volume_title = self.current_volume.volume_title

        # 更新book_detail中的卷大纲
        self.book_detail.volume_outlines[self.volume_index] = refined_volume

        # 保存细化后的大纲到本地文件
        file_path = f"{self.book_name}/{self.book_id}/outline/第{refined_volume.volume_number}卷-细化大纲-v{refined_volume.version}.md"
        FileSystemUtils.write_content_to_workspace(file_path, refined_volume.get_completed_contents())
        logger.info(f"[{self.node_name}] ✅第 {refined_volume.volume_number} 卷细化大纲已保存: {file_path}")

        # 准备状态更新
        state_update = {"book_detail": self.book_detail}

        # 继续细化下一卷或进入章节创作
        return Command(update=state_update, goto=self.node_name)

    def on_get_dump_tag(self):
        """获取状态存档的标签"""
        if self.stage == "init":
            return "outlines"
        else:
            return "outline_refined"


async def writer_outline_node(state: State, config: RunnableConfig) -> Command[Literal["chapter", "__end__"]]:
    """
    Writer大纲节点：基于完成的策划案创建详细的书籍大纲
    """
    return await WriterOutlineNode(state, config).execute()


# 1. 生成整书分卷大纲
class BookVolumeOutlinesSchema(Serializable):
    """书籍详情"""

    volume_outlines: List[VolumeOutline] = Field(default_factory=list, description="卷大纲列表")


__all__ = ["WriterOutlineNode", "writer_outline_node", "BookVolumeOutlinesSchema"]
