import logging
import typing
from typing import Any, Literal, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, SystemMessage
from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.common.llms import get_llm
from src.common.utils.filesystem_utils import FileSystemUtils
from src.common.utils.llm_request_utils import llm_structured_request
from src.common.utils.token_utils import TokenTools
from src.state import State
from src.nodes.character.character_prompt import *
from src.nodes.common.base_node import LLMJsonNode, MultiStageNode
from src.nodes.common.book_types import (
    CharacterDetail,
    CharacterDetailCollection,
    CharacterSummary,
    CharacterSummaryCollection,
    WriterPlan,
)
from src.nodes.common.store_manager import WriterStoreManager


logger = logging.getLogger(__name__)


class WriterCharacterNode(MultiStageNode):
    def __init__(self, state: State, config: RunnableConfig):
        super().__init__("writer_character", state, config)

    def _determine_stage(self, state: State) -> str:
        """确定当前应该执行哪个阶段"""
        character_summaries = state.get("character_summaries", None)
        character_details = state.get("character_details", None)

        if character_summaries is None or not character_summaries.characters:
            return "init_character"
        elif character_details is None or len(character_details.characters) < len(character_summaries.characters):
            return "detailed_character"
        else:
            return "completed"

    def _get_schema_for_stage(self, stage: str):
        """根据阶段返回对应的schema"""
        if stage == "init_character":
            return CharacterSummaryCollection
        elif stage == "detailed_character":
            return CharacterDetail
        else:
            return None

    def _init_stage_data(self) -> None:
        """初始化阶段相关数据"""
        # 获取基础数据
        self.writer_current_plan: WriterPlan = self.state.get("writer_current_plan")
        self.refined_book_setting = self.state.get("refined_book_setting", "暂无")
        self.character_summaries: Optional[CharacterSummaryCollection] = self.state.get("character_summaries", None)
        self.character_details: Optional[CharacterDetailCollection] = self.state.get("character_details", None)

        # 初始化角色详细设计相关数据
        if self.stage == "detailed_character":
            if self.character_details is None:
                self.character_details = CharacterDetailCollection(characters=[])

            # 找到下一个需要细化的角色
            completed_names = [char.name for char in self.character_details.characters]
            self.next_character = None
            if self.character_summaries:
                for char_summary in self.character_summaries.characters:
                    if char_summary.name not in completed_names:
                        self.next_character = char_summary
                        break

    async def _check_prerequisites(self) -> Optional[Command]:
        """检查前置条件"""
        # 前置步骤检查
        if not self.writer_current_plan.is_all_step_completed():
            raise AssertionError("❌ 策划案未完成，无法创建角色设计")
        if not self.writer_current_plan.is_all_step_refined():
            raise AssertionError("❌ 策划案未完成细化，无法创建角色设计")
        if self.book_id is None:
            raise AssertionError("book_id 为空")
        return None

    async def _get_completion_next_node(self) -> str:
        """获取完成后的下一个节点"""
        return "writer_outline"

    async def _generate_stage_prompt(self, stage: str) -> list[BaseMessage]:
        """生成指定阶段的prompt"""
        if stage == "init_character":
            logger.info(f"角色设计阶段1，生成角色简介")
            return self._generate_init_character_prompt()
        elif stage == "detailed_character":
            logger.info(f"角色设计阶段2，细化角色详情")
            return self._generate_detailed_character_prompt()
        else:
            raise ValueError(f"Unknown stage: {stage}")

    async def _handle_stage_response(self, stage: str, response_parsed: Any) -> Command:
        """处理指定阶段的响应"""
        if stage == "init_character":
            return await self._handle_init_character_response(response_parsed)
        elif stage == "detailed_character":
            return await self._handle_detailed_character_response(response_parsed)
        else:
            raise ValueError(f"Unknown stage: {stage}")

    def _generate_init_character_prompt(self):
        """生成初始角色设计的prompt"""
        prompt_list = []
        character_prompt = character_initial_design_template.format(
            json_schema=CharacterSummaryCollection.model_json_schema(),
            refined_book_setting=self.refined_book_setting,
            book_name=self.writer_current_plan.book_name,
            book_description=self.writer_current_plan.book_description,
        )
        prompt_list.append(character_prompt)

        # 添加demo章节（如果有）
        demo_chapter = self.config["configurable"].get("demo_chapter", None)
        if demo_chapter is not None:
            demo_chapter_prompt = character_demo_chapter_template.format(demo_chapter=demo_chapter)
            prompt_list.append(demo_chapter_prompt)

        prompt = "\n\n".join(prompt_list)
        input_messages = [SystemMessage(content=prompt)]
        return input_messages

    def _generate_detailed_character_prompt(self):
        """生成详细角色设计的prompt"""
        # 数据已在__init__中初始化，直接使用
        assert self.next_character is not None, "没有找到需要细化的角色"

        prompt_list = []
        other_characters_content = self.character_summaries.get_prompt_content(exclude_name=self.next_character.name)
        target_summary = self.next_character.get_full_content()
        character_prompt = character_detailed_design_template.format(
            json_schema=CharacterDetail.model_json_schema(),
            refined_book_setting=self.refined_book_setting,
            book_name=self.writer_current_plan.book_name,
            book_description=self.writer_current_plan.book_description,
            character_name=self.next_character.name,
            target_summary=target_summary,
            other_characters=other_characters_content,
        )

        prompt_list.append(character_prompt)
        prompt = "\n\n".join(prompt_list)
        input_messages = [SystemMessage(content=prompt)]
        return input_messages



    async def _handle_init_character_response(self, response_parsed: CharacterSummaryCollection) -> Command:
        """处理初始角色设计响应"""
        assert len(response_parsed.characters) > 0, "❌ 角色简介创建失败"

        character_summaries = response_parsed
        logger.info(f"[{self.node_name}] ✅角色简介创建完成: {len(character_summaries.characters)}个角色")

        # 保存到本地文件
        file_path = f"{self.book_name}/{self.book_id}/character/角色简介.md"
        FileSystemUtils.write_content_to_workspace(file_path, character_summaries.get_characters_content())
        logger.info(f"[{self.node_name}] ✅角色简介已保存到本地文件: {file_path}")

        # 返回命令继续流程
        state_update = {"character_summaries": character_summaries}
        return Command(update=state_update, goto=self.node_name)

    async def _handle_detailed_character_response(self, response_parsed: CharacterDetail) -> Command:
        """处理详细角色设计响应"""
        # 将新的角色详情添加到集合中
        self.character_details.characters.append(response_parsed)

        logger.info(f"[{self.node_name}] ✅角色详情创建完成: {response_parsed.name}")
        logger.info(
            f"[{self.node_name}] 已完成 {len(self.character_details.characters)}/{len(self.character_summaries.characters)} 个角色的详情设计"
        )

        # 保存当前进度到本地文件
        if len(self.character_details.characters) == len(self.character_summaries.characters):
            # 所有角色都完成了，保存完整的详情文件
            file_path = f"{self.book_name}/{self.book_id}/character/角色详情.md"
            FileSystemUtils.write_content_to_workspace(file_path, self.character_details.get_characters_content())
            logger.info(f"[{self.node_name}] ✅所有角色详情已保存到本地文件: {file_path}")

        # 保存单个角色的详情
        file_path = f"{self.book_name}/{self.book_id}/character/角色详情_{'0' if len(self.character_details.characters) <10 else ''}{len(self.character_details.characters)}_{response_parsed.name}.md"
        single_char_content = f"# {response_parsed.get_full_content()}\n"

        FileSystemUtils.write_content_to_workspace(file_path, single_char_content)
        logger.info(f"[{self.node_name}] ✅{response_parsed.name}详情已保存到本地文件: {file_path}")

        # 返回命令继续流程
        state_update = {"character_details": self.character_details}
        return Command(update=state_update, goto=self.node_name)

    def on_get_dump_tag(self):
        """获取状态存档的标签"""
        if self.stage == "init":
            return "character_summaries"
        else:
            return "character_details"


async def writer_character_node(state: State, config: RunnableConfig) -> Command[Literal["writer_outline", "__end__"]]:
    """面向对象版本的角色设计节点"""
    return await WriterCharacterNode(state, config).execute()


__all__ = ["WriterCharacterNode", "writer_character_node"]
