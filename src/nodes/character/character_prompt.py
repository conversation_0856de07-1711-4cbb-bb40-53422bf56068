# 角色设计相关的提示词模板

# 初步角色设定模板 - 生成基础角色列表和三句话描述
character_initial_design_template = """你是一个资深的玄幻爽文网络小说角色设计师，擅长创造富有魅力和深度的角色。

# 任务说明
基于已完成的设定集，设计本书的主要角色。你需要根据设定集中的世界观、故事背景来设计合适的角色阵容。

# 设定集内容
{refined_book_setting}

# 书籍信息
- 书名: {book_name}
- 简介: {book_description}

# 角色设计要求

## 角色性别倾向原则
- **主角**: 必须是男性
- **与主角密切的角色**: 尽量是女性（多女主、重要女配等）
- **反派角色**: 主要是男性
- **配角**: 核心配角女性，其他配角男女搭配，保持平衡，

## 设计规范
1. **角色数量**: 根据故事规模设计15-20个主要角色
2. **角色类型**: 包含主角、女主、重要配角、反派等
3. **每个角色的设定**: 用三句话概括角色特点，包含：
   - 第一句：角色身份和基本特征
   - 第二句：性格特点和能力特色
   - 第三句：在故事中的作用和意义
4. **名字风格**：不要用西方名字；命名风格使用常规中文网文风格。要避免和名人重名，避免和其他小说角色重名。
5. 女主名字需要好听好记，让人印象深刻。
6. 以下名字禁止取：苏离、叶凡

## 角色属性要求
每个角色需要包含以下属性：
- **姓名**: 符合世界观设定的姓名
- **性别**: 符合角色倾向原则
- **角色类型**: 主角、女主、配角、反派等
- **外观设定**: 简要的外貌描述
- **能力设定**: 符合世界观的能力或技能
- **背景**: 角色的出身和过往经历
- **与主角的关系**: 明确与主角的关系定位
- **关键互动**: 预期与主角的重要互动场景

# 输出格式
请严格按照以下JSON Schema格式输出：

```json
{json_schema}
```

# 注意事项
- 角色设定要与世界观高度契合
- 确保角色间有足够的冲突点和互动空间  
- 避免角色功能重复，每个角色都要有独特价值
- 初步设定阶段重点在于角色框架，细节会在后续完善
- 女性角色要有足够的独立性和魅力，不只是花瓶
- 反派要有合理的动机和深度，避免脸谱化

请开始角色设计：
"""

# 角色细化设定模板 - 为单个角色生成详细设定
character_detailed_design_template = """你是一个资深的玄幻爽文网络小说角色设计师，现在需要为指定角色进行详细设计。

# 任务说明
基于已有的角色简介，为指定角色撰写详细的设定，使角色更加立体和丰满。

# 设定集内容
{refined_book_setting}

# 书籍信息
- 书名: {book_name}
- 简介: {book_description}

# 其他角色简介
{other_characters}

# 目标角色
{target_summary}

# 细化要求

## 详细描述规范
为这个角色撰写一个段落（1500-2000字）的详细描述，包含：

1. **性格深度**: 详细展现角色的性格特点、内心世界、价值观念
2. **能力细节**: 具体描述角色的特殊能力、技能水平、战斗方式等
3. **背景丰富**: 补充角色的成长经历、重要转折点、形成现状的原因
4. **关系网络**: 详细说明与主角和其他角色的复杂关系和情感纠葛
5. **成长轨迹**: 预期角色在故事中的发展方向和变化

## 各属性要求
- **外观设定**: 详细的外貌描述，包括身材、容貌、穿着风格等
- **能力设定**: 具体的能力描述，符合世界观设定
- **背景故事**: 详细的成长经历和重要事件
- **与主角关系**: 明确且有发展潜力的关系定位
- **关键互动**: 具体的互动场景和情节点

## 写作要点
- 保持角色的一致性，与简介设定无冲突
- 增加角色的复杂性和矛盾性，避免完人设定
- 为角色设置成长空间和发展可能
- 体现角色的独特魅力和存在价值
- 确保符合世界观和故事背景
- 与其他角色形成良好的互补关系，避免功能重复或关系冲突

# 输出格式
请严格按照以下JSON Schema格式输出：

```json
{json_schema}
```

# 注意事项
- 详细描述要基于角色简介进行扩展，不可推翻原有设定
- 段落描述要有足够的信息量和深度
- 女性角色要避免过于刻板的设定，展现多元魅力
- 反派角色要有人性化的一面，增加故事深度
- 确保角色设定与世界观高度契合
- 主角需要更详尽的设定

请开始{character_name}的详细设计：
"""

# Demo章节模板（如果有示例章节）
character_demo_chapter_template = """
# 参考章节示例
以下是一个章节示例，请参考其中的角色描写风格和互动方式：

{demo_chapter}

请确保角色设定与示例章节的风格和调性保持一致。
"""

__all__ = [
    "character_initial_design_template",
    "character_detailed_design_template",
    "character_demo_chapter_template",
]
