dump state支持hash，然后弄一个current state专门用于恢复、回滚



现在我正在制作一个自动化写作的ai agent。
我的小说体量是千万字规模，我的思路是
1. 先由用户给定一个创意，由ai生成一份完整的设定集
2. 根据设定集，生成一份完整的分卷大纲，5卷，每卷500章，每章5000字。
3. 根据每卷的大纲，逐步撰写每一章节


现在遇到最大的难点是，ai的上下文是有长度限制的，大概在10万字左右，
需要克服的具体困难主要有
1. 生成设定集后，设定集过长，8万字左右，已经接近了上下文限制
2. 生成分卷大纲后，我还需要规划每章的大纲，也需要合理的上下文管理。


现在初步的应对方案是
1. 生成设定集后，再次调用llm，让ai来提炼总结设定集，这可能能起到一定的效果，但可能会有设定丢失的风险。
2. 让大模型从上至下
2.1. 为每卷生成50章的内容大纲
2.2. 为每50章生成每10章的大纲
2.3. 为每10章生成每章的大纲
3. 写作过程中，使用滑动窗口的方式管理上下文
3.1. 生成章节时的上下文输入：当前卷的大纲、当前卷的总结、前10章的摘要、前一章后1000字、当前章以及后4章的大纲、完整设定集


1. 规划设定集
输入-idea、示例章节
输出-规划
2. 生成设定集
输入-plan、示例章节






重构
改造雪花法 

