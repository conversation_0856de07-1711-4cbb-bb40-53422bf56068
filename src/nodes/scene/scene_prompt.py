# 场景设计相关的提示词模板

# 场景设计模板 - 为指定卷生成场景清单
volume_scene_design_template = """你是一个资深的玄幻爽文网络小说场景设计师，擅长根据雪花写作法进行场景规划。

# 任务说明
基于已完成的设定集、角色设计和卷大纲，为第{volume_number}卷设计详细的场景清单。

# 设定集内容
{refined_book_setting}

# 角色设计内容
{character_design}

# 当前卷大纲
{volume_outline}

# 重要约束
- **只能使用以上角色设计中已明确设定的角色**
- **严禁创造或引入角色设计中没有的新角色**
- **龙套角色、路人甲乙、临时出现的小角色等均不应出现在场景设计中**
- **场景应专注于已设定角色的互动和发展**

# 场景设计要求

## 设计原则
- 每个场景都要有明确的目的和推进作用
- 场景间要有逻辑连接和情节推进
- 每个场景要突出关键矛盾冲突
- 合理安排角色出场，避免人物过于集中或分散

## 场景属性规范
每个场景需要包含以下属性：
- **场景编号**: 在该卷内的连续编号
- **地点**: 具体的场景发生地点
- **主要出场角色**: 在该场景中出现的主要角色姓名（仅限已设定的角色）
- **核心事件**: 该场景中发生的关键事件（1-3个）
- **场景概要**: 该场景的主要内容和发展
- **矛盾冲突**: 该场景中的主要冲突点（1-3个）
- **场景目的**: 该场景在整体故事中的作用和意义
- **预计章节**: 该场景预计需要的章节数量（1-5章）

## 设计规模
- 每卷设计8-15个主要场景
- 场景总章节数要接近每卷100章的体量
- 重要场景可安排更多章节，过渡场景可压缩

## 注意事项
- 确保场景推进符合角色成长轨迹
- 平衡动作场面和情感描写场景
- 为关键角色安排足够的互动场景
- 保持与整体故事风格的一致性

# 输出格式
请严格按照以下JSON Schema格式输出：

```json
{json_schema}
```

请开始为第{volume_number}卷设计场景清单：
"""

# Demo章节模板（如果有示例章节）
scene_demo_chapter_template = """
# 参考章节示例
以下是一个章节示例，请参考其中的场景构建和节奏控制：

{demo_chapter}

请确保场景设计与示例章节的风格和节奏保持一致。
"""

__all__ = [
    "volume_scene_design_template",
    "scene_demo_chapter_template",
]
