import logging
import typing
from typing import Any, Literal, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, SystemMessage
from langgraph.config import RunnableConfig
from langgraph.types import Command

from src.common.llms import get_llm
from src.common.utils.filesystem_utils import FileSystemUtils
from src.common.utils.llm_request_utils import llm_structured_request
from src.common.utils.token_utils import TokenTools
from src.state import State
from src.nodes.scene.scene_prompt import *
from src.nodes.common.book_types import *
from src.nodes.common.base_node import LLMJsonNode, MultiStageNode
from src.nodes.common.store_manager import WriterStoreManager


logger = logging.getLogger(__name__)


def get_current_chapter_number(book_detail: Optional[BookDetail]) -> int:
    """
    获取当前章节编号

    Args:
        book_detail: 书籍详情

    Returns:
        int: 当前章节编号（从1开始）
    """
    if book_detail is None or not book_detail.chapters:
        return 1  # 如果还没有章节，当前是第1章
    return len(book_detail.chapters) + 1  # 已完成章节数 + 1


class WriterSceneNode(MultiStageNode):
    def __init__(self, state: State, config: RunnableConfig):
        super().__init__("writer_scene", state, config)



    def _determine_stage(self, state: State) -> str:
        """确定当前应该执行哪个阶段"""
        scene_design = state.get("scene_design", None)
        book_detail = state.get("book_detail", None)
        current_chapter = get_current_chapter_number(book_detail)

        if scene_design is None or not scene_design.volume_scenes:
            return "init_structure"
        elif scene_design.need_scene_design_for_current_progress(current_chapter):
            return "design_volume"
        else:
            return "completed"

    def _get_schema_for_stage(self, stage: str):
        """根据阶段返回对应的schema"""
        if stage == "design_volume":
            return VolumeSceneDesign
        else:
            return BookSceneDesign

    def _init_stage_data(self) -> None:
        """初始化阶段相关数据"""
        # 获取基础数据
        self.writer_current_plan: WriterPlan = self.state.get("writer_current_plan")
        self.character_details = self.state.get("character_details", None)
        self.character_summaries = self.state.get("character_summaries", None)
        self.book_detail: Optional[BookDetail] = self.state.get("book_detail", None)
        self.scene_design: Optional[BookSceneDesign] = self.state.get("scene_design", None)
        self.refined_book_setting = self.state.get("refined_book_setting", None)

        # 初始化场景设计相关数据
        self.current_chapter = get_current_chapter_number(self.book_detail)
        self.next_volume = None
        if self.stage == "design_volume" and self.scene_design is not None:
            self.next_volume = self.scene_design.get_next_incomplete_volume_for_current_progress(self.current_chapter)

    async def _check_prerequisites(self) -> Optional[Command]:
        """检查前置条件"""
        # 前置步骤检查
        if not self.writer_current_plan.is_all_step_completed():
            raise AssertionError("❌ 策划案未完成，无法创建场景设计")
        if not self.writer_current_plan.is_all_step_refined():
            raise AssertionError("❌ 策划案未完成细化，无法创建场景设计")
        if self.book_id is None:
            raise AssertionError("book_id 为空")

        # 检查角色设计是否完成
        if self.character_summaries is None:
            raise AssertionError("❌ 角色简介未完成，无法创建场景设计")
        if self.character_details is None:
            raise AssertionError("❌ 角色详情未完成，无法创建场景设计")
        if len(self.character_details.characters) != len(self.character_summaries.characters):
            raise AssertionError("❌ 角色详情未完全完成，无法创建场景设计")

        # 检查大纲是否完成
        if self.book_detail is None:
            raise AssertionError("❌ 大纲未生成，无法创建场景设计")

        # 基于当前章节进度检查大纲是否足够
        if not self.book_detail.is_outline_ready_for_writing(self.current_chapter):
            raise AssertionError("❌ 当前章节进度所需的大纲未完成，无法创建场景设计")

        return None

    async def _get_completion_next_node(self) -> str:
        """获取完成后的下一个节点"""
        return "writer_chapter"

    async def _generate_stage_prompt(self, stage: str) -> list[BaseMessage]:
        """生成指定阶段的prompt"""
        if stage == "init_structure":
            return await self._generate_init_structure_prompt()
        elif stage == "design_volume":
            return await self._generate_design_volume_prompt()
        else:
            raise ValueError(f"Unknown stage: {stage}")

    async def _handle_stage_response(self, stage: str, response_parsed: Any) -> Command:
        """处理指定阶段的响应"""
        if stage == "init_structure":
            return await self._handle_init_structure_response(response_parsed)
        elif stage == "design_volume":
            return await self._handle_design_volume_response(response_parsed)
        else:
            raise ValueError(f"Unknown stage: {stage}")

    async def execute(self) -> Command:
        """重写execute方法以处理init_structure阶段"""
        if self.stage == "init_structure":
            return await self._init_scene_design_structure()
        else:
            return await super().execute()

    async def _init_scene_design_structure(self) -> Command:
        """初始化场景设计结构 - 为每个卷创建空的场景设计"""
        logger.info("初始化场景设计结构")

        # 创建场景设计结构
        volume_scenes = []
        for volume_outline in self.book_detail.volume_outlines:
            volume_scene = VolumeSceneDesign(
                volume_number=volume_outline.volume_number, scenes=[], design_completed=False
            )
            volume_scenes.append(volume_scene)

        scene_design = BookSceneDesign(volume_scenes=volume_scenes)

        # 更新状态
        state_update = {"scene_design": scene_design}

        return Command(update=state_update, goto=self.node_name)

    async def _generate_init_structure_prompt(self) -> list[BaseMessage]:
        """生成初始化结构的prompt（实际上不需要LLM）"""
        return []

    async def _generate_design_volume_prompt(self) -> list[BaseMessage]:
        """生成设计卷场景的prompt"""
        logger.info(f"基于当前章节进度({self.current_chapter})，设计第{self.next_volume}卷的场景清单")

        # 获取当前卷的大纲
        current_volume_outline = None
        for volume_outline in self.book_detail.volume_outlines:
            if volume_outline.volume_number == self.next_volume:
                current_volume_outline = volume_outline
                break

        if current_volume_outline is None:
            raise AssertionError(f"找不到第{self.next_volume}卷的大纲")

        # 获取角色信息
        character_content = self.character_details.get_characters_content()

        # 构建提示词
        prompt_list = []
        scene_prompt = volume_scene_design_template.format(
            json_schema=VolumeSceneDesign.model_json_schema(),
            volume_number=self.next_volume,
            refined_book_setting=self.refined_book_setting,
            character_design=character_content,
            volume_outline=current_volume_outline.get_completed_contents(),
        )
        prompt_list.append(scene_prompt)

        # 添加demo章节（如果有）
        demo_chapter = self.config["configurable"].get("demo_chapter", None)
        if demo_chapter is not None:
            demo_chapter_prompt = scene_demo_chapter_template.format(demo_chapter=demo_chapter)
            prompt_list.append(demo_chapter_prompt)

        prompt = "\n\n".join(prompt_list)
        input_messages = [SystemMessage(content=prompt)]
        return input_messages

    async def _handle_init_structure_response(self, response_parsed: Any) -> Command:
        """处理初始化结构响应（实际上不会被调用）"""
        return Command(update={}, goto=self.node_name)

    async def _handle_design_volume_response(self, response_parsed: VolumeSceneDesign) -> Command:
        """处理设计卷场景响应"""
        if len(response_parsed.scenes) == 0:
            raise AssertionError(f"❌ 第{self.next_volume}卷场景设计创建失败")

        # 设置为完成状态
        response_parsed.design_completed = True
        response_parsed.volume_number = self.next_volume

        # 更新场景设计中的对应卷
        volume_scene_design = self.scene_design.get_volume_scenes(self.next_volume)
        if volume_scene_design:
            volume_scene_design.scenes = response_parsed.scenes
            volume_scene_design.design_completed = True
        else:
            logger.error(f"找不到第{self.next_volume}卷的场景设计结构")

        logger.info(f"[{self.node_name}] ✅第{self.next_volume}卷场景设计完成: {len(response_parsed.scenes)}个场景")

        # 保存到本地文件
        file_path = f"{self.book_name}/{self.book_id}/scene/第{self.next_volume}卷场景清单.md"
        FileSystemUtils.write_content_to_workspace(file_path, response_parsed.get_scenes_content())
        logger.info(f"[{self.node_name}] ✅第{self.next_volume}卷场景清单已保存: {file_path}")

        # 存储到数据库
        if self.store_manager:
            await self.store_manager.store_scene_design(self.scene_design)

        # 准备状态更新
        state_update = {"scene_design": self.scene_design}

        # 返回命令继续流程
        return Command(update=state_update, goto=self.node_name)

    def get_dump_tag(self) -> str:
        """获取状态存档的标签"""
        if self.stage == "init_structure":
            return "scene_init"
        else:
            return f"scene_volume_{self.next_volume}"


async def writer_scene_node(state: State, config: RunnableConfig) -> Command[Literal["chapter", "__end__"]]:
    """
    Writer场景设计节点：基于完成的大纲创建详细的场景清单
    """
    # 使用新的面向对象实现
    return await WriterSceneNode(state, config).execute()


__all__ = ["WriterSceneNode", "writer_scene_node"]
