# Writer Steps执行阶段的Prompt模板

from langchain_core.prompts import PromptTemplate

# 步骤执行的详细指导prompt
STEP_EXECUTION_DETAILED_PROMPT = """
现在需要执行特定的创作步骤。请参考以下信息进行深度创作。

# 用户的原始要求
{user_original_request}

## 项目背景
- **书名**: {writer_current_plan.book_name}
- **简介**: {writer_current_plan.book_description}
- **策划思路**: {writer_current_plan.plan_thought}
- **当前完整策划案如下，包含已完成内容和未完成的标题框架**:

```markdown
<refined_book_setting>
{refined_book_setting}
</refined_book_setting>
<unfinished_contents>
{unfinished_contents}
</unfinished_contents>
```

## 当前执行任务
**步骤**: {current_step.step_title}
**描述**: {current_step.step_description}

## 执行指导原则

### 1. 世界观建构任务
如果当前子步骤涉及世界观设定，请从宏观到微观，系统性地构建玄幻世界的各个层面。
重点关注：独特性、合理性、扩展性、与剧情的结合度。
考虑以下维度展开：
- **空间架构**: 多维空间体系（凡灵仙神混沌等层级）
- **种族势力**: 各种族生态链和势力形态
- **规则系统**: 自然规则、能量规则、特殊法则
- **历史脉络**: 时空坐标轴和关键历史节点
- **核心设定**: 至宝体系和跨维度核心道具

### 2. 角色体系设计任务

如果当前子步骤涉及人物设计，请创造立体、有魅力的角色群体。
重点关注：人物动机、性格特征、成长弧线、角色关系。
考虑以下维度展开：
- **主角塑造**: 驱动锚点、立体人格、成长轨迹
- **反派设计**: 利益型、理念型、宿命型的三维反派
- **功能角色**: 引路人、羁绊者、对立者的功能定位
- **关系网络**: 角色间的复杂关系和互动逻辑

### 3. 力量体系设计任务
如果当前子步骤涉及修炼体系，请设计平衡且有趣的修炼/战斗体系。
重点关注：层级合理、进阶有趣、战斗精彩、符合世界观。
考虑以下维度展开：
- **能量运转**: 源力本质和修行路径树
- **境界划分**: 层级体系和跃迁模型
- **特权系统**: 主角专属能力和差异化金手指
- **技能体系**: 基础技到自创技的成长树


## 输出要求

1. **内容质量**: 确保输出符合网文爽文标准，具有强烈的画面感和代入感
2. **结构完整**: 输出内容要有清晰的逻辑结构和层次
3. **创新性**: 避免落入俗套，要有独特的创意和亮点，但整体设定不要过于复杂
4. **内容纯粹**: 不要输出任何与创作无关的内容，如：“好的，我明白了”、“我明白了”、“好的”、“建议”等

请开始执行这个子步骤
"""


# Prompt模板
bs_step_execution_template = PromptTemplate(
    template=STEP_EXECUTION_DETAILED_PROMPT,
    input_variables=[
        "user_original_request",
        "writer_current_plan",
        "current_step",
        "refined_book_setting",
        "unfinished_contents",
    ],
)

bs_refine_prompt_template = PromptTemplate(
    template="""
<用户的原始要求>
{user_original_request}
</用户的原始要求>

你是专业的玄幻爽文网络小说创作助手，我们正在逐步撰写设定集，需要每写一部分做一次重构梳理，确保不会信息爆炸，也能保障一致性。

以下是现有设定集和新增内容：

<现有设定集>
{refined_book_setting}
</现有设定集>

<新增内容>
{new_contents}
</新增内容>

请仔细阅读思考，并整合梳理两部分内容，输出新的完整设定集

注意：

1. 如果有前后冲突的设定，请对齐进行优化，只保留一个更能吸引读者的设定。
2. 不要遗漏重要的信息，但可以对比较无聊的设定进行优化。
3. 你的输出将会直接输出给用户，不要有任何备注和废话。
4. **注意要符合爽文风格和爽文市场要求**。

""",
    input_variables=[
        "user_original_request",
        "refined_book_setting",
        "new_contents",
    ],
)

__all__ = [
    "bs_step_execution_template",
    "bs_refine_prompt_template",
]
