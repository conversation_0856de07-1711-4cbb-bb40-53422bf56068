import logging
from typing import Literal, Optional
from uuid import uuid4

from langchain_core.messages import AIMessage, SystemMessage
from langgraph.config import RunnableConfig
from langgraph.types import Command
from src.common.utils.filesystem_utils import FileSystemUtils
from src.common.utils.llm_request_utils import llm_structured_request
from src.common.utils.time_utils import get_prompt_timestamp
from src.state import State

from ..common.base_node import LLM<PERSON>sonNode, StructuredDataNode
from src.nodes.book_setting.bs_planner_prompt import *
from src.nodes.common.book_types import *
from src.nodes.common.store_manager import WriterStoreManager

from src.nodes.outlines.outline_prompt import *
import logging
import typing
from collections.abc import AsyncIterator
from typing import Any, List, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage

from langchain_core.runnables.utils import Output
from src.common.llms import get_llm
from src.common.utils.message_utils import MessageUtils
from src.common.utils.token_utils import TokenTools

logger = logging.getLogger(__name__)


class BSPlannerNode(StructuredDataNode):
    def __init__(self, state: State, config: RunnableConfig):
        super().__init__("bs_planner", state, config, WriterPlanSchema)

    async def pre_execute_check(self) -> Optional[Command]:
        """执行前检查"""
        return None

    async def generate_prompt(self):
        """生成LLM输入提示"""
        prompt_list = []
        base_prompt = base_prompt_template.format(CURRENT_TIME=get_prompt_timestamp())
        prompt_list.append(base_prompt)

        # 规定书名
        book_name = self.config["configurable"].get("book_name", None)
        if book_name is not None:
            book_name_prompt = f"书名使用《{book_name}》"
            prompt_list.append(book_name_prompt)

        # 添加demo章节
        demo_chapter = self.config["configurable"].get("demo_chapter", None)
        if demo_chapter is not None:
            demo_chapter_prompt = bs_demo_chapter_template.format(demo_chapter=demo_chapter)
            prompt_list.append(demo_chapter_prompt)

        # 添加demo设定
        demo_bs = self.config["configurable"].get("demo_bs", None)
        if demo_bs is not None:
            demo_bs_prompt = bs_demo_bs_template.format(demo_bs=demo_bs)
            prompt_list.append(demo_bs_prompt)

        # 规划格式
        plan_prompt = plan_template.format(
            max_step_num=5,
        )
        prompt_list.append(plan_prompt)
        json_prompt = json_template.format(
            json_schema=WriterPlanSchema.model_json_schema(),
        )
        prompt_list.append(json_prompt)
        prompt = "\n\n".join(prompt_list)

        input_messages = [SystemMessage(content=prompt), *self.state["messages"]]
        return input_messages

    async def update_data(self, data: WriterPlanSchema) -> dict:
        """更新状态中的数据"""
        plan = data.convert_to_writer_plan()
        book_id = str(uuid4())
        book_name = plan.book_name

        # 从 state.messages 中提取用户原始诉求
        user_original_request = ""
        for message in self.state.get("messages", []):
            if hasattr(message, "type") and message.type == "human":
                user_original_request = message.content
                break

        # 更新实例属性以便其他方法使用
        self.book_id = book_id
        self.book_name = book_name
        self.plan = plan
        self.data = data

        return {
            "book_name": book_name,
            "book_id": book_id,
            "writer_current_plan": plan,
            "user_original_request": user_original_request,
        }

    async def get_file_path(self) -> str:
        """获取文件保存路径"""
        return "setting/0-setting_plan.md"

    async def format_content_for_file(self, data: WriterPlanSchema) -> str:
        """格式化数据为文件内容"""
        return self.plan.get_completed_contents()

    async def save_data_to_database(self, data: WriterPlanSchema) -> None:
        """保存数据到数据库"""
        if self.book_id:
            store_manager = WriterStoreManager(book_id=self.book_id)
            await store_manager.store_writer_plan(plan=self.plan)

    async def get_next_node(self) -> str:
        """获取下一个节点名称"""
        return "bs_steps"


async def bs_planner_node(state: State, config: RunnableConfig) -> Command[Literal["bs_steps"]]:
    return await BSPlannerNode(state, config).execute()
