import logging
from typing import Literal, Optional

from langgraph.types import Command, Send
from src.common.llms import get_llm
from src.common.utils.filesystem_utils import FileSystemUtils
from src.common.utils.llm_request_utils import get_response_from_astream
from src.common.utils.token_utils import TokenTools
from src.state import State

from ...common.utils.time_utils import get_prompt_timestamp
from src.nodes.book_setting.bs_planner_prompt import *
from src.nodes.book_setting.bs_steps_prompt import *
from src.nodes.common.book_types import *
from langchain_core.load.dump import dumps
from langchain_core.messages import SystemMessage
from langgraph.config import RunnableConfig
from src.nodes.common.base_node import ContentGenerationNode, LLMJsonNode
from src.nodes.common.store_manager import WriterStoreManager


logger = logging.getLogger(__name__)


class BSStepsNode(ContentGenerationNode):
    def __init__(self, state: State, config: RunnableConfig):
        super().__init__("bs_steps", state, config)
        self.writer_current_plan: WriterPlan = state.get("writer_current_plan")
        self.refined_book_setting = state.get("refined_book_setting", "暂无")

        # 初始化当前步骤相关数据
        if self.writer_current_plan and self.writer_current_plan.plan_steps:
            try:
                self.step_index, self.is_last_step = self.writer_current_plan.find_next_step()
                self.current_step: WriterPlanStep = self.writer_current_plan.plan_steps[self.step_index]
            except (IndexError, TypeError):
                self.step_index = 0
                self.is_last_step = True
                self.current_step = None
        else:
            self.step_index = 0
            self.is_last_step = True
            self.current_step = None

    async def pre_execute_check(self) -> Optional[Command]:
        """节点执行前检查"""
        self.logger.info("bs_steps node is executing current plan.")

        # 检查writer_current_plan是否存在
        if not self.writer_current_plan:
            self.logger.error(f"[{self.node_name}] writer_current_plan is None")
            return Command(goto="__end__")

        # 检查当前节点是否已完成工作
        if self.writer_current_plan.is_all_step_completed():
            self.logger.warning(f"[{self.node_name}] All steps are completed, no need to execute steps")
            return Command(goto="bs_refine")

        # 寻找下一个未执行的步骤（只用于检查，不保存）
        step_index, _ = self.writer_current_plan.find_next_step()
        # 如果上一个步骤还没归纳梳理
        if step_index > 0 and self.writer_current_plan.plan_steps[step_index - 1].finsh_and_todo_refine():
            self.logger.warning(f"[{self.node_name}] Previous step is not refined, need to refine")
            return Command(goto="bs_refine")

        # 返回None表示继续执行
        return None

    async def generate_prompt(self):
        """生成LLM输入提示"""
        # 数据已在__init__中初始化，直接使用
        self.logger.info(f"[{self.node_name}] Executing step: {self.current_step.step_title}")
        self.logger.info(
            f"[{self.node_name}] current_step: {self.current_step.model_dump_json(indent=4, exclude_none=False)}"
        )
        # 使用详细的prompt模板
        base_prompt = base_prompt_template.format(
            CURRENT_TIME=get_prompt_timestamp(),
        )
        execution_prompt = bs_step_execution_template.format(
            user_original_request=self.state.get("user_original_request"),
            writer_current_plan=self.writer_current_plan,
            current_step=self.current_step,
            refined_book_setting=self.refined_book_setting,
            unfinished_contents=self.writer_current_plan.get_unrefined_contents(),
        )
        prompt_list = [base_prompt + execution_prompt]

        # 规定书名
        book_name = self.config["configurable"].get("book_name", None)
        if book_name is not None:
            book_name_prompt = f"书名使用《{book_name}》"
            prompt_list.append(book_name_prompt)

        # 添加demo章节
        demo_chapter = self.config["configurable"].get("demo_chapter", None)
        if demo_chapter is not None:
            demo_chapter_prompt = bs_demo_chapter_template.format(demo_chapter=demo_chapter)
            prompt_list.append(demo_chapter_prompt)

        # 添加demo设定
        demo_bs = self.config["configurable"].get("demo_bs", None)
        if demo_bs is not None and self.refined_book_setting != "暂无":
            demo_bs_prompt = bs_demo_bs_template.format(demo_bs=demo_bs)
            prompt_list.append(demo_bs_prompt)

        prompt = "\n\n".join(prompt_list)
        input_messages = [SystemMessage(content=prompt)]
        approximately_input_tokens = TokenTools.count_messages_approximately_tokens(input_messages)
        self.logger.info(f"[{self.node_name}] approximately_input_tokens: {approximately_input_tokens}")

        return input_messages

    async def update_content(self, content: str) -> dict:
        """更新状态中的内容"""
        self.current_step.step_execution_res = content
        return {"writer_current_plan": self.writer_current_plan}

    async def get_file_path(self) -> str:
        """获取文件保存路径"""
        return f"setting/{self.step_index+1}-{self.current_step.step_title}.md"

    async def save_content_to_database(self, content: str) -> None:
        """保存内容到数据库"""
        if self.store_manager:
            try:
                await self.store_manager.store_step_result(
                    step=self.current_step,
                    result_content=content,
                )
                # 同时更新数据库中的策划案状态
                await self.store_manager.store_writer_plan(plan=self.writer_current_plan)
            except RuntimeError as e:
                if "Called get_config outside of a runnable context" in str(e):
                    self.logger.warning(f"[{self.node_name}] 无法保存到数据库（不在运行上下文中）")
                else:
                    raise

    async def get_next_node(self) -> str:
        """获取下一个节点名称"""
        next_node = self.writer_current_plan.get_next_node()
        self.logger.info(f"[{self.node_name}] WriterStep '{self.current_step.step_title}' execution completed")
        return next_node


async def bs_steps_node(state: State, config: RunnableConfig) -> Command[Literal["bs_refine", "human_review"]]:
    return await BSStepsNode(state, config).execute()


def bs_step_condition_edge(
    state: State, config: RunnableConfig
) -> Command[Literal["bs_steps", "bs_refine", "human_review"]]:
    logger.info("bs_step_condition_edge node is executing current plan.")
    node_name = "bs_step_condition_edge"

    # 检查是否有人工审核参数，如果有则跳转到审核
    human_review_params = state.get("human_review_params")

    if human_review_params:
        next_node = "human_review"
    else:
        writer_current_plan: WriterPlan = state.get("writer_current_plan")
        assert writer_current_plan is not None
        next_node = writer_current_plan.get_next_node()
    logger.info(f"[{node_name}] jumping to {next_node}")

    return next_node


class BSRefineNode(ContentGenerationNode):
    def __init__(self, state: State, config: RunnableConfig):
        super().__init__("bs_refine", state, config)
        self.writer_current_plan: WriterPlan = state.get("writer_current_plan")
        self.refined_book_setting = state.get("refined_book_setting", "暂无")

        # 初始化精炼相关数据
        self.refine_id = None
        self.refine_step = None
        self.refine_contents = None

        # 如果有计划且需要精炼，则初始化精炼数据
        if self.writer_current_plan and not self.writer_current_plan.is_all_step_refined():
            try:
                self.refine_id, self.refine_step = next(
                    (id, step)
                    for id, step in enumerate(self.writer_current_plan.plan_steps)
                    if step.finsh_and_todo_refine()
                )
                self.refine_contents = self.refine_step.get_step_content()
            except StopIteration:
                # 没找到需要精炼的步骤，保持为None
                pass

    async def pre_execute_check(self) -> Optional[Command]:
        """节点执行前检查"""
        self.logger.info("bs_refine node is executing current plan.")

        # 检查是否所有步骤都已精炼
        if self.writer_current_plan and self.writer_current_plan.is_all_step_refined():
            self.logger.info("[bs_refine] All steps are refined, moving to writer_outline")
            return Command(goto="writer_outline")

        # 检查是否有有效的精炼数据
        if self.refine_step is None:
            self.logger.error("[bs_refine] No step found for refinement")
            return Command(goto="writer_outline")

        return None

    async def generate_prompt(self):
        """生成LLM输入提示"""
        refine_prompt = bs_refine_prompt_template.format(
            user_original_request=self.state.get("user_original_request"),
            refined_book_setting=self.refined_book_setting,
            new_contents=self.refine_contents,
        )
        prompt_list = [refine_prompt]

        # 添加demo章节
        demo_chapter = self.config["configurable"].get("demo_chapter", None)
        if demo_chapter is not None and self.refined_book_setting != "暂无":
            demo_chapter_prompt = bs_demo_chapter_template.format(demo_chapter=demo_chapter)
            prompt_list.append(demo_chapter_prompt)

        # 添加demo设定
        demo_bs = self.config["configurable"].get("demo_bs", None)
        if demo_bs is not None:
            demo_bs_prompt = bs_demo_bs_template.format(demo_bs=demo_bs)
            prompt_list.append(demo_bs_prompt)

        prompt = "\n\n".join(prompt_list)
        input_messages = [SystemMessage(content=prompt)]

        return input_messages

    async def update_content(self, content: str) -> dict:
        """更新状态中的内容"""
        if self.refine_id == 0:
            refined_book_setting = self.refine_contents
        else:
            refined_book_setting = content

        # 标记步骤已精炼
        self.refine_step.step_has_refined = True

        return {
            "refined_book_setting": refined_book_setting,
            "writer_current_plan": self.writer_current_plan,
        }

    async def get_file_path(self) -> str:
        """获取文件保存路径"""
        return f"setting/0.{self.refine_id}-refined-booksetting.md"

    async def save_content_to_database(self, content: str) -> None:
        """保存内容到数据库"""
        if self.store_manager:
            try:
                refined_book_setting = content if self.refine_id != 0 else self.refine_contents
                await self.store_manager.store_refined_book_setting(refined_book_setting)
            except RuntimeError as e:
                if "Called get_config outside of a runnable context" in str(e):
                    self.logger.warning(f"[{self.node_name}] 无法保存到数据库（不在运行上下文中）")
                else:
                    raise

    async def get_next_node(self) -> str:
        """获取下一个节点名称"""
        self.logger.info(f"[{self.node_name}] refined book setting execution completed")
        return "writer_outline" if self.writer_current_plan.is_all_step_refined() else "bs_steps"


async def bs_refine_node(state: State, config: RunnableConfig) -> Command[Literal["bs_steps", "writer_outline"]]:
    return await BSRefineNode(state, config).execute()


__all__ = [
    "BSStepsNode",
    "bs_steps_node",
    "bs_step_condition_edge",
    "bs_refine_node",
    "BSRefineNode",
]
