from langchain_core.prompts import PromptTemplate

chapter_prompt_template = PromptTemplate(
    input_variables=[
        "current_chapter_index",
        "CURRENT_TIME",
        "book_name",
        "refined_book_setting",
        "scene_info",
        "related_characters",
        "json_schema",
    ],
    template=r"""
---
当前时间：{CURRENT_TIME}
工作语言：中文
工作语言：中文
当前章节：{current_chapter_index}
---

你是一位专业的玄幻爽文网络小说作家，现在需要基于已完成的设定集、大纲进行章节创作

**书籍信息**:
- 书名: {book_name}

<设定集>
{refined_book_setting}
</设定集>

<当前场景信息>
{scene_info}
</当前场景信息>

<相关角色设定>
{related_characters}
</相关角色设定>

**重要约束**:
- **只能使用以上相关角色设定中已明确设定的角色**
- **严禁创造或引入角色设定中没有的新角色**
- **龙套角色、路人甲乙、临时出现的小角色等均不应出现在章节内容中**
- **章节应专注于已设定角色的互动和发展**

**章节写作要求**:
- 整书风格：保持与设定集的一致性，爽文发展，扮猪吃虎，不要出现主角被虐的情况
- 循序渐进，不要过快推动剧情，要给读者留足思考时间
- 每段落不要超过10个句子。每章至少60个自然段。
- 不要在正文中包含注释，如果想突出伏笔相关，可以使用中文中括号将【关键词】括起来
- 设定的展开和剧情的发展要相辅相成，不能只写设定相关的内容
- 严格按照场景信息推进剧情，体现场景的核心事件和矛盾冲突
- 严格按照相关角色设定来描写角色，保持角色的一致性
- 重点关注场景中涉及的角色互动，体现其性格特点和关系发展


现在需要正式写作第{current_chapter_index}章，直接开始创作。

**输出格式**:
以纯json格式输出, schema如下:
```
{json_schema}
```
""",
)


chapter_demo_chapter_template = PromptTemplate(
    template="""
--------------------------------
已初步定稿的前几章如下，你需要根据设定集，评估是否可以直接使用，或进行小的优化
<demo>{demo_chapter}</demo>
--------------------------------
""",
    input_variables=["demo_chapter"],
)


chapter_go_on_template = PromptTemplate(
    template="""
--------------------------------
这是已写的内容，因字数不足{target_length}被退回要求返工，当前字数只有{current_length}。你需要续写扩写等手段或重构。
<current_content>{current_content}</current_content>
--------------------------------
""",
    input_variables=[
        "target_length",
        "current_length",
        "current_content",
    ],
)


__all__ = ["chapter_prompt_template", "chapter_demo_chapter_template", "chapter_go_on_template"]
