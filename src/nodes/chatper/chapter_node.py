import logging
import typing
from typing import Any, Literal, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, SystemMessage
from langchain_core.runnables.config import RunnableConfig
from langgraph.types import Command
from src.common.utils.filesystem_utils import FileSystemUtils
from src.common.utils.llm_request_utils import llm_structured_request
from src.common.utils.time_utils import get_prompt_timestamp
from src.nodes.common.book_types import BookDetail, Chapter, WriterPlan

from src.state import State
from src.nodes.chatper.chapter_prompt import *
from src.nodes.common.base_node import LLMJsonNode, IterativeProcessingNode


logger = logging.getLogger(__name__)


class ChapterRuntimeState:
    writed_chapters_cnt: int = 0


kChapterLengthDefaultConfig = 3000


class ChapterNode(IterativeProcessingNode):
    def __init__(self, state: State, config: RunnableConfig):
        super().__init__("chapter", state, config, Chapter)

    def _init_iteration_data(self) -> None:
        """初始化迭代相关数据"""
        # 获取基础数据
        self.writer_current_plan = self.state.get("writer_current_plan")
        self.book_detail = self.state.get("book_detail", None)
        self.refined_book_setting = self.state.get("refined_book_setting", None)
        self.character_details = self.state.get("character_details", None)
        self.character_summaries = self.state.get("character_summaries", None)
        self.scene_design = self.state.get("scene_design", None)

        # 初始化章节创作相关数据
        if self.book_detail:
            self.current_chapter_index = len(self.book_detail.chapters) + 1
            self.current_chapter_len = 0

            # 如果上一章字数不足
            if (
                len(self.book_detail.chapters) > 0
                and len(self.book_detail.chapters[-1].chapter_content) < kChapterLengthDefaultConfig
            ):
                self.current_chapter_index = len(self.book_detail.chapters)
                self.current_chapter_len = len(self.book_detail.chapters[-1].chapter_content)

    async def _check_prerequisites(self) -> Optional[Command]:
        """检查前置条件"""
        # 前置步骤检查
        if not self.writer_current_plan.is_all_step_completed():
            raise AssertionError("❌ 策划案未完成，无法进行章节创作")
        if self.book_id is None:
            raise AssertionError("book_id 为空")

        # 检查角色设计是否完成
        if self.character_summaries is None:
            raise AssertionError("❌ 角色简介未完成，无法进行章节创作")
        if self.character_details is None:
            raise AssertionError("❌ 角色详情未完成，无法进行章节创作")
        if len(self.character_details.characters) != len(self.character_summaries.characters):
            raise AssertionError("❌ 角色详情未完全完成，无法进行章节创作")

        # 检查场景设计是否完成
        if self.scene_design is None:
            raise AssertionError("❌ 场景设计未完成，无法进行章节创作")
        if not self.scene_design.is_all_volumes_completed():
            raise AssertionError("❌ 场景设计未完成，无法进行章节创作")

        return None

    async def _is_all_iterations_completed(self) -> bool:
        """检查是否所有迭代都已完成"""
        # 检查是否还有场景需要创作
        if self.scene_design:
            current_scene = self.scene_design.get_scene_for_chapter(self.current_chapter_index)
            return current_scene is None
        return True

    async def _get_completion_next_node(self) -> str:
        """获取完成后的下一个节点"""
        return "__end__"

    async def _get_current_iteration_item(self) -> Any:
        """获取当前迭代的项目"""
        # 获取当前章节对应的场景
        current_scene = self.scene_design.get_scene_for_chapter(self.current_chapter_index)
        if current_scene is None:
            logger.error(f"[章节节点] 找不到第{self.current_chapter_index}章对应的场景")
            raise ValueError(f"找不到第{self.current_chapter_index}章对应的场景")
        return current_scene

    async def _generate_iteration_prompt(self, current_item: Any) -> list[BaseMessage]:
        """生成当前迭代的prompt"""
        current_scene = current_item
        logger.info(
            f"[{self.node_name}] 第{self.current_chapter_index}章使用场景{current_scene.scene_number}: {current_scene.location}"
        )

        # 根据场景筛选相关角色
        related_characters_info = []
        if current_scene.featured_characters:
            for char_name in current_scene.featured_characters:
                char = self.character_details.get_character_by_name(char_name)
                if char:
                    # 构建角色信息
                    char_info = f"**{char.name}（{char.role_type}）**\n"
                    char_info += f"- **性别**: {char.gender}\n"
                    char_info += f"- **详细设定**: {char.detailed_description}\n"
                    char_info += f"- **外观**: {char.appearance}\n"
                    char_info += f"- **能力**: {char.abilities}\n"
                    char_info += f"- **背景**: {char.background}\n"
                    char_info += f"- **与主角关系**: {char.relationship_with_protagonist}\n"
                    related_characters_info.append(char_info)

        related_characters_text = (
            "\n\n".join(related_characters_info) if related_characters_info else "本场景无特定相关角色"
        )

        # 提示词
        prompt_list: list[str] = []

        # 添加demo章节
        demo_chapter_cnt = self.config["configurable"].get("demo_chapter_cnt", 0)
        if self.current_chapter_index < demo_chapter_cnt:
            demo_chapter = self.config["configurable"].get("demo_chapter", None)
            if demo_chapter is not None:
                demo_chapter_prompt = chapter_demo_chapter_template.format(demo_chapter=demo_chapter)
                prompt_list.append(demo_chapter_prompt)

        # 当前章节
        chapter_prompt: str = chapter_prompt_template.format(
            current_chapter_index=self.current_chapter_index,
            json_schema=Chapter.model_json_schema(),
            CURRENT_TIME=get_prompt_timestamp(),
            book_name=self.writer_current_plan.book_name,
            refined_book_setting=self.refined_book_setting,
            scene_info=current_scene.get_scene_content(),
            related_characters=related_characters_text,
        )
        prompt_list.append(chapter_prompt)

        if self.current_chapter_len > 0:
            chapter_go_on_prompt = chapter_go_on_template.format(
                target_length=kChapterLengthDefaultConfig + 1000,
                current_length=self.current_chapter_len,
                current_content=self.book_detail.chapters[-1].chapter_content,
            )
            prompt_list.append(chapter_go_on_prompt)

        if self.current_chapter_index > 1:
            if self.current_chapter_len > 0:
                last_chapter = self.book_detail.chapters[-1]
                prompt_list.append(f"目前相关信息总结如下(包含最新章)\n{last_chapter.to_next_chapter_prompt()}")
            else:
                last_chapter = self.book_detail.chapters[-1]
                prompt_list.append(f"上一章结束后, 相关信息总结如下\n{last_chapter.to_next_chapter_prompt()}")

        prompt = "\n\n".join(prompt_list)
        input_messages = [SystemMessage(content=prompt)]

        logger.info(
            f"[{self.node_name}] ✅开始{'生成' if self.current_chapter_len == 0 else '重写'}第{self.current_chapter_index}章"
        )
        return input_messages

    async def _handle_iteration_response(self, response_parsed: Chapter) -> Command:
        """处理迭代响应"""
        chapter: Chapter = response_parsed

        if chapter.chapter_number != self.current_chapter_index:
            raise AssertionError("章节编号不一致")

        # 1. 解析并处理响应
        logger.info(
            f"[{self.node_name}] ✅第{chapter.chapter_number}章 {chapter.chapter_title}生成完成: len={len(chapter.chapter_content)}"
        )

        # 更新章节本地文件
        if self.current_chapter_len > 0:
            old_chapter = self.book_detail.chapters[-1]
            old_file_path = f"{self.book_name}/{self.book_id}/chapter/第{self.current_chapter_index}章-{old_chapter.chapter_title}.md"
            FileSystemUtils.delete_file_from_workspace(old_file_path)
            self.book_detail.chapters[self.current_chapter_index - 1] = chapter
        else:
            self.book_detail.chapters.append(chapter)

        file_path = f"{self.book_name}/{self.book_id}/chapter/第{self.current_chapter_index}章-{chapter.chapter_title}.md"
        FileSystemUtils.write_content_to_workspace(file_path, chapter.get_completed_content())
        logger.info(f"[{self.node_name}] ✅章节已保存到本地文件: {file_path}, len={len(chapter.chapter_content)}")

        # 3. 准备状态更新
        state_update = {"book_detail": self.book_detail}

        # 4. agent 下一步
        write_chapters_cnt = self.config["configurable"].get("write_chapters_cnt", 5)
        ChapterRuntimeState.writed_chapters_cnt += 1
        if ChapterRuntimeState.writed_chapters_cnt >= write_chapters_cnt:
            logger.info(f"[{self.node_name}] ✅已写完{write_chapters_cnt}章，结束")

        return Command(
            update=state_update,
            goto=self.node_name if ChapterRuntimeState.writed_chapters_cnt < write_chapters_cnt else "__end__",
        )


async def chapter_node(state: State, config: RunnableConfig) -> Command[Literal["chapter", "__end__"]]:
    """面向对象版本的章节创作节点"""
    return await ChapterNode(state, config).execute()


__all__ = ["ChapterNode", "chapter_node"]
