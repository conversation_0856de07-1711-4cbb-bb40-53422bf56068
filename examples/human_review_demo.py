#!/usr/bin/env python3
"""
人工审核功能演示脚本

这个脚本展示了如何在创作流程中启用人工审核功能。
"""

import asyncio

from src.graph.graph import qflow_graph
from src.state import State


async def demo_human_review():
    """演示人工审核功能"""

    print("🚀 启动人工审核功能演示")
    print("=" * 60)

    # 准备配置，启用人工审核
    config = {
        "configurable": {
            "workspace_path": "workspace",
            "enable_human_review": True,  # 启用人工审核
            "book_name": "测试小说",
            # 可以添加其他配置...
        }
    }

    # 准备初始状态
    initial_state = State(
        user_original_request="创建一个科幻小说，主角是一个程序员，意外获得了改写现实代码的能力",
        messages=[],
    )

    print("📋 配置信息:")
    print(f"- 启用人工审核: {config['configurable']['enable_human_review']}")
    print(f"- 用户需求: {initial_state['user_original_request']}")
    print()

    try:
        async with qflow_graph() as graph:
            print("🔄 开始执行创作流程...")
            print("💡 当遇到审核环节时，您可以:")
            print("   - 输入 '/pass' 通过审核")
            print("   - 输入修改建议让AI重新生成")
            print()

            # 执行图
            result = await graph.ainvoke(initial_state, config=config)

            print("\n✅ 流程执行完成!")
            print(f"📊 最终状态包含 {len(result)} 个字段")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断了执行")
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {e}")


def main():
    """主函数"""
    print("人工审核功能演示")
    print("这个演示将展示如何在书籍设定流程中使用人工审核")
    print()

    # 运行演示
    asyncio.run(demo_human_review())


if __name__ == "__main__":
    main()
