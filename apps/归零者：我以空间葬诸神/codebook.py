import asyncio
import logging
import os
import uuid
from pathlib import Path

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from src import qflow_graph

from src.init_log import init_log

thread_id = f"{Path(__file__).parent.name}_{str(uuid.uuid4())}"
config = {
    # "recursion_limit": 5,
    "configurable": {
        "thread_id": thread_id,
        "workspace_path": Path(__file__).parent / "workspace",
    },
}


init_log()

logger = logging.getLogger(__name__)


async def main():
    async with qflow_graph() as graph:

        human_msg = "使用刘慈欣风格策划一篇网文玄幻小说，主角有时间、空间相关的超能力，主调是魔法修炼体系，1000万字规模. 爽文，不写逃亡剧情，不要出现主角被虐的情况"
        demo_chapter = open(Path(__file__).parent / "demo_chapter.txt", "r").read()
        demo_bs = open(Path(__file__).parent / "demo_bs.txt", "r").read()
        llm_input = {"messages": [HumanMessage(content=human_msg)]}
        config["configurable"]["demo_bs"] = demo_bs
        config["configurable"]["demo_chapter"] = demo_chapter
        config["configurable"]["demo_chapter_cnt"] = 2
        config["configurable"]["write_chapters_cnt"] = 10  # 一次执行写10次
        config["configurable"]["book_name"] = "归零者：我以空间葬诸神"
        # config["configurable"]["resume_book_id"] = "cde088ff-5613-4dba-937a-3f60b47d077f"

        async for event in graph.astream(llm_input, config, stream_mode="values", debug=False):
            logger.info(event)
            if "messages" in event:
                event["messages"][-1].pretty_print()

        ## 后面是交互式 暂时用不到
        async def stream_graph_updates(user_input: str):
            input = {"messages": [{"role": "user", "content": user_input}]}
            async for event in graph.astream(input, config, stream_mode="values", debug=False):
                logger.info(event)
                if "messages" in event:
                    event["messages"][-1].pretty_print()

        while True:
            try:
                user_input = input("请输入问题:")
                await stream_graph_updates(user_input)
            except:
                import traceback

                logger.error(traceback.format_exc())
                break


asyncio.run(main())
