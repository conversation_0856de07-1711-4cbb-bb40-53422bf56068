import asyncio
from src import graph


async def test_react_agent_simple_passthrough() -> None:
    res = await graph.ainvoke(
        {"messages": [("user", "谁是 LangChain 的创建者?")]},
        {"configurable": {"system_prompt": "你是一个帮助用户解决问题的AI助手。"}},
    )

    print(res)
    assert "harrison" in str(res["messages"][-1].content).lower()

if __name__ == "__main__":
    asyncio.run(test_react_agent_simple_passthrough())