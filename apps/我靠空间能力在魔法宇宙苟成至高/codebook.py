import asyncio
import logging
import os
import uuid
from pathlib import Path

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from src import qflow_graph

from src.init_log import init_log

thread_id = f"{Path(__file__).parent.name}_{str(uuid.uuid4())}"
config = {
    # "recursion_limit": 5,
    "configurable": {
        "thread_id": thread_id,
        "workspace_path": Path(__file__).parent / "workspace",
    },
}


init_log()

logger = logging.getLogger(__name__)


async def main():
    async with qflow_graph() as graph:

        human_msg = "使用刘慈欣风格策划一篇网文玄幻小说，主角有时间、空间相关的超能力，魔法修炼体系，1000万字规模"
        demo_chapter = open(Path(__file__).parent / "demo_chapter.txt", "r").read()
        input = {"messages": [HumanMessage(content=human_msg)]}
        config["configurable"]["demo_chapter"] = demo_chapter
        config["configurable"]["demo_chapter_cnt"] = 3
        config["configurable"]["demo_chapter_cnt"] = 3
        config["configurable"]["write_chapters_cnt"] = 5  # 一次执行写5章
        config["configurable"]["book_name"] = "我靠空间能力在魔法宇宙苟成至高"

        async for event in graph.astream(input, config, stream_mode="values", debug=False):
            logger.info(event)
            if "messages" in event:
                event["messages"][-1].pretty_print()

        ## 后面是交互式 暂时用不到
        async def stream_graph_updates(user_input: str):
            input = {"messages": [{"role": "user", "content": user_input}]}
            async for event in graph.astream(input, config, stream_mode="values", debug=False):
                logger.info(event)
                if "messages" in event:
                    event["messages"][-1].pretty_print()

        while True:
            try:
                user_input = input("请输入问题:")
                await stream_graph_updates(user_input)
            except:
                import traceback

                logger.error(traceback.format_exc())
                break


asyncio.run(main())
