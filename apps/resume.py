import asyncio
import logging
import os
import uuid

from src import qflow_graph

from src.init_log import init_log


thread_id = f"{os.path.basename(__file__)}_{str(uuid.uuid4())}"
config = {"configurable": {"thread_id": thread_id}}


config = {
    "recursion_limit": 5,
    "configurable": {
        "thread_id": "new_book.py_0f4c7ff3-b4e5-4a3e-a333-e9ff4d6a62d3",
        # "thread_id": thread_id,
        "checkpoint_id": "1f03970d-c360-69ac-8001-a0c05d4b559c",
        # "checkpoint_ns": "writer_team|writer_steps:4c5fa2e1-d99a-9365-9b44-895c95b0ad21",
    },
}

init_log()

logger = logging.getLogger(__name__)


async def main():
    async with qflow_graph() as graph:
        # 从 checkpoint 恢复并继续执行
        async def stream_graph_updates(user_input: str):
            input = {"messages": [{"role": "user", "content": user_input}]}
            async for event in graph.astream(input, config, stream_mode="values", debug=False):
                logger.info(event)
                if "messages" in event:
                    event["messages"][-1].pretty_print()

        # 首先检查 checkpoint 状态
        logger.info(f"正在从 checkpoint 恢复: {config}")
        messages = {"messages": []}
        async for event in graph.astream(messages, config, stream_mode="values", debug=False):
            logger.info(event)
            if "messages" in event and event["messages"]:
                event["messages"][-1].pretty_print()

        # 继续交互式对话
        logger.info("进入交互模式...")
        while True:
            try:
                user_input = input("请输入问题:")
                if user_input.lower() in ["quit", "exit", "退出"]:
                    break
                await stream_graph_updates(user_input)
            except KeyboardInterrupt:
                logger.info("用户中断程序")
                break
            except Exception as e:
                import traceback

                logger.error(f"发生错误: {e}")
                logger.error(traceback.format_exc())
                break


if __name__ == "__main__":
    asyncio.run(main())
