import asyncio
import logging
import os
import uuid
from pathlib import Path

from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from src import qflow_graph

from src.init_log import init_log


thread_id = f"{os.path.basename(__file__)}_{str(uuid.uuid4())}"
config = {
    # "recursion_limit": 5,
    "configurable": {
        "thread_id": thread_id,
        "workspace_path": Path(__file__).parent / "workspace",
    },
}


init_log()

logger = logging.getLogger(__name__)


async def main():
    async with qflow_graph() as graph:

        human_msg = """
请策划一部玄幻爽文小说, 包括宏大的世界观、修炼成长体系与法则、完善的战斗逻辑、有特色看点亮点等。
- 修炼体系不喜欢金丹筑基类的，倾向于偏魔法玄幻。
- 主角掌控极其罕见的空间与时间之力，其他角色大多是普通的魔法体系。
- 世界观宏大，史诗感强，有深度，伏笔设计精彩。
- 整体采用爽文发展，扮猪吃虎等各种爽点，不要出现主角被虐的情况
- 不要搞熵增熵减、熵值、系统管理员等这种大众看不懂的，或者脱离主流小说的奇怪设定
"""
        llm_input = {"messages": [HumanMessage(content=human_msg)]}
        config["configurable"]["write_chapters_cnt"] = 10  # 一次执行写10次
        config["configurable"]["resume_book_id"] = "452da6e4-64d3-49c0-9143-6de86595be8a"
        config["configurable"]["enable_human_review"] = True

        async for event in graph.astream(llm_input, config, stream_mode="values", debug=False):
            pass
            # logger.info(event)
            # if "messages" in event:
            #     event["messages"][-1].pretty_print()

        async def stream_graph_updates(user_input: str):
            input = {"messages": [{"role": "user", "content": user_input}]}
            async for event in graph.astream(input, config, stream_mode="values", debug=False):
                logger.info(event)
                if "messages" in event:
                    event["messages"][-1].pretty_print()

        ## 后面是交互式 暂时用不到
        async def stream_graph_updates(user_input: str):
            input = {"messages": [{"role": "user", "content": user_input}]}
            async for event in graph.astream(input, config, stream_mode="values", debug=False):
                logger.info(event)
                if "messages" in event:
                    event["messages"][-1].pretty_print()

        while True:
            try:
                user_input = input("请输入问题:")
                await stream_graph_updates(user_input)
            except:
                import traceback

                logger.error(traceback.format_exc())
                break


asyncio.run(main())
